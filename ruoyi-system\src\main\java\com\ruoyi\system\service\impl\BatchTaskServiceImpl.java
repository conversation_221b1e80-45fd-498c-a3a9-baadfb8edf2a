package com.ruoyi.system.service.impl;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.Country;
import com.ruoyi.system.domain.CertType;
import com.ruoyi.system.domain.dto.request.BatchTaskCreateDTO;
import com.ruoyi.system.domain.mongo.BatchUploadTask;
import com.ruoyi.system.domain.mongo.FolderInfo;
import com.ruoyi.system.domain.mongo.ImageRepository;
import com.ruoyi.system.repository.BatchUploadTaskRepository;
import com.ruoyi.system.repository.FolderInfoRepository;
import com.ruoyi.system.repository.ImageRepositoryRepo;

import com.ruoyi.system.service.IBatchTaskService;
import com.ruoyi.system.service.ICertTypeService;
import com.ruoyi.system.service.ICountryService;
import com.ruoyi.system.service.IFolderInfoService;
import com.ruoyi.system.service.IVersionNameParseService;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.system.domain.dto.response.BatchUploadTaskVO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import com.mongodb.client.result.UpdateResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.Arrays;


/**
 * 批量上传任务服务实现类
 * 
 * 实现批量断点续传功能的核心业务逻辑，包括任务创建、文件上传完成处理等功能。
 * 
 * 主要职责：
 * 1. 创建和管理批量上传任务
 * 2. 处理Tus文件上传完成回调
 * 3. 维护任务进度和状态
 * 4. 协调FolderInfo和ImageRepository的数据入库
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@Service
@RequiredArgsConstructor
public class BatchTaskServiceImpl implements IBatchTaskService {

    private static final Logger log = LoggerFactory.getLogger(BatchTaskServiceImpl.class);

    // Repository依赖注入
    private final BatchUploadTaskRepository batchUploadTaskRepository;
    private final FolderInfoRepository folderInfoRepository;
    private final ImageRepositoryRepo imageRepositoryRepo;

    // MySQL服务依赖注入
    private final ICountryService countryService;
    private final ICertTypeService certTypeService;

    // 文件夹服务依赖注入
    private final IFolderInfoService folderInfoService;

    // 版本解析服务依赖注入
    private final IVersionNameParseService versionNameParseService;

    // 部门服务依赖注入
    private final ISysDeptService sysDeptService;

    // MongoDB模板，用于复杂的Upsert操作
    private final MongoTemplate mongoTemplate;

    /**
     * 创建批量上传任务（简化版）
     *
     * @param dto 任务创建请求DTO
     * @return 创建的批量上传任务实体
     * @throws ServiceException 当参数无效时抛出业务异常
     */
    @Override
    @Transactional
    public BatchUploadTask createBatchTask(BatchTaskCreateDTO dto) throws ServiceException {
        log.info("开始创建批量上传任务，文件夹数: {}, 总文件数: {}",
                dto.getFolderCount(), dto.getTotalFileCount());

        try {
            // 验证DTO
            if (!dto.isValid()) {
                throw new ServiceException("任务创建参数无效");
            }

            // 1. 创建主任务
            BatchUploadTask task = new BatchUploadTask();
            task.setTaskId(UUID.randomUUID().toString());

            // 生成任务名称
            if (dto.getTaskName() != null && !dto.getTaskName().trim().isEmpty()) {
                task.setTaskName(dto.getTaskName());
            } else {
                String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
                String taskType = dto.isMultiFolder() ? "多文件夹批量上传任务" : "批量上传任务";
                task.setTaskName(timestamp + "_" + taskType);
            }

            task.setDescription(dto.getDescription());
            task.setStatus("UPLOADING");
            task.setTotalFiles(dto.getTotalFileCount());
            task.setTotalFolders(dto.getFolderCount());
            task.setProcessedFiles(0);
            task.setProcessedFolders(0);
            task.setCreateTime(new Date());
            task.setUpdateTime(new Date());

            // 2. 保存主任务
            BatchUploadTask savedTask = batchUploadTaskRepository.save(task);
            log.info("主任务创建成功，taskId: {}", savedTask.getTaskId());

            // 3. 批量创建文件夹信息
            List<String> folderIds = new ArrayList<>();
            for (BatchTaskCreateDTO.FolderInfoDTO folderDto : dto.getFolders()) {
                String folderId = createFolderInfoFromDTO(savedTask.getTaskId(), folderDto);
                folderIds.add(folderId);
                log.info("文件夹信息创建成功: {} -> {}", folderDto.getFolderName(), folderId);
            }

            log.info("批量上传任务创建完成，taskId: {}, 创建了{}个文件夹",
                    savedTask.getTaskId(), folderIds.size());
            return savedTask;

        } catch (Exception e) {
            log.error("创建批量上传任务失败", e);
            if (e instanceof ServiceException) {
                throw e;
            }
            throw new ServiceException("创建批量上传任务失败: " + e.getMessage());
        }
    }

    /**
     * 从DTO创建文件夹信息
     */
    private String createFolderInfoFromDTO(String taskId, BatchTaskCreateDTO.FolderInfoDTO folderDto) {
        try {
            // 查询国家信息
            Country country = countryService.selectCountryById(folderDto.getCountryId());
            if (country == null) {
                throw new ServiceException("国家信息不存在，ID: " + folderDto.getCountryId());
            }

            // 查询证件类型信息
            CertType certType = certTypeService.selectCertTypeById(folderDto.getCertTypeId());
            if (certType == null) {
                throw new ServiceException("证件类型不存在，ID: " + folderDto.getCertTypeId());
            }

            // 创建FolderInfo
            FolderInfo folderInfo = new FolderInfo();
            folderInfo.setFolderId(UUID.randomUUID().toString());
            folderInfo.setFileInfoId(UUID.randomUUID().toString()); // 设置唯一的fileInfoId
            folderInfo.setTaskId(taskId);
            folderInfo.setFolderName(folderDto.getFolderName());
            folderInfo.setFolderPath(folderDto.getFolderPath());
            folderInfo.setCountryInfo(country);
            folderInfo.setCertInfo(certType);
            folderInfo.setIssueYear(folderDto.getIssueYear());

                        // 调用 parseFolderNameToVersionInfo 解析文件夹名称生成预解析版本信息
            try {
                log.info("开始解析文件夹名称生成预解析版本信息: {}", folderDto.getFolderName());
                FolderInfo.PreParseVersionInfo parseInfo = versionNameParseService.parseFolderNameToVersionInfo(folderDto.getFolderName());
                
                // 直接使用解析结果，设置解析时间
                parseInfo.setParseTime(new Date());
                folderInfo.setPreParseVersionInfo(parseInfo);
                
                log.info("预解析版本信息设置完成: parseStatus={}, parsedVersionCode={}", 
                    parseInfo.getParseStatus(), parseInfo.getParsedVersionCode());
                    
            } catch (Exception e) {
                log.warn("解析文件夹名称失败，将创建基础预解析信息: {}, 错误: {}", folderDto.getFolderName(), e.getMessage());
                
                // 如果解析失败，创建基础的预解析信息
                FolderInfo.PreParseVersionInfo preParseInfo = new FolderInfo.PreParseVersionInfo();
                preParseInfo.setParseStatus("FAILED");
                preParseInfo.setParsedVersionCode(null);
                preParseInfo.setParseTime(new Date());
                preParseInfo.setParseErrors(Arrays.asList("解析失败: " + e.getMessage()));
                
                folderInfo.setPreParseVersionInfo(preParseInfo);
            }

            folderInfo.setFileCount(folderDto.getFileCount());
            folderInfo.setProcessedFileCount(0);
            folderInfo.setStatus("PROCESSING");
            folderInfo.setFolderType("regular"); // 默认为普通样本
            folderInfo.setCreateTime(new Date());
            folderInfo.setUpdateTime(new Date());

            FolderInfo savedFolder = folderInfoRepository.save(folderInfo);
            return savedFolder.getFolderId();

        } catch (Exception e) {
            log.error("创建文件夹信息失败: {}", folderDto.getFolderName(), e);
            throw new ServiceException("创建文件夹信息失败: " + e.getMessage());
        }
    }





    /**
     * 处理文件上传完成回调
     *
     * 这是整个业务的枢纽方法，负责协调FolderInfo和ImageRepository的数据入库，
     * 并维护任务进度状态。当Tus服务器完成单个文件上传时调用此方法。
     * 支持多级文件夹路径映射和原始文件名保持。
     *
     * @param taskId 任务ID，用于关联任务和文件
     * @param issuePlace 签发地，用于填充FolderInfo
     * @param certNumberPrefix 证号前缀，用于构建MinIO路径
     * @param originalFileName 用户上传的原始文件名
     * @param finalMinioPath 文件在MinIO中的最终存储路径
     * @param folderName 原始文件夹名称，用于存储在FolderInfo中
     * @param folderPath 完整文件夹路径，用于匹配对应的FolderInfo记录
     * @throws ServiceException 当taskId无效或数据库操作失败时抛出业务异常
     */
    @Override
    public void handleUploadCompletion(String taskId, String issuePlace, String certNumberPrefix,
                                     String originalFileName, String finalMinioPath, String folderName, String folderPath) throws ServiceException {
        log.info("处理文件上传完成回调，taskId: {}, 签发地: {}, 证号前缀: {}, 原始文件名: {}, MinIO路径: {}, 文件夹名称: {}, 文件夹路径: {}",
                taskId, issuePlace, certNumberPrefix, originalFileName, finalMinioPath, folderName, folderPath);

        try {
            // 1. 获取任务上下文 - 通过taskId查找BatchUploadTask
            Optional<BatchUploadTask> taskOpt = batchUploadTaskRepository.findByTaskId(taskId);
            if (!taskOpt.isPresent()) {
                log.error("无法找到taskId对应的任务: {}", taskId);
                return; // 异常情况，直接返回
            }
            BatchUploadTask task = taskOpt.get();
            log.debug("找到任务: {}, 任务名称: {}", taskId, task.getTaskName());

            // 2. 根据文件夹路径查找对应的FolderInfo记录
            String folderId = findFolderInfoByPath(taskId, folderPath, folderName, task, issuePlace);
            log.debug("找到或创建FolderInfo，folderId: {}", folderId);

            // 3. 获取FolderId - 执行完upsert后再次查询获取folderId
            if (folderId == null) {
                log.error("无法获取folderId，任务ID: {}", taskId);
                return; // 严重错误，直接返回
            }

            // 4. 检查是否已存在相同文件记录，避免重复处理
            if (isImageAlreadyExists(folderId, originalFileName, finalMinioPath)) {
                log.warn("文件已存在，跳过重复处理: taskId={}, folderId={}, fileName={}",
                    taskId, folderId, originalFileName);
                return;
            }

            // 5. 检查并设置主图路径
            checkAndSetMainPicture(folderId, originalFileName, finalMinioPath);

            // 6. 处理ImageRepository(Insert) - 创建新的ImageRepository记录，使用原始文件名
            createImageRepositoryWithOriginalName(task, folderId, originalFileName, finalMinioPath, folderName);
            log.debug("ImageRepository记录创建完成，使用原始文件名: {}", originalFileName);

            // 7. 更新BatchUploadTask状态 - 更新进度并检查任务是否完成
            updateTaskProgress(taskId, folderId);
            log.debug("任务进度更新完成");

            log.info("文件上传完成处理成功，taskId: {}, folderId: {}, 文件: {}",
                    taskId, folderId, originalFileName);

        } catch (Exception e) {
            log.error("处理文件上传完成回调失败，taskId: {}, 文件: {}", taskId, originalFileName, e);
            if (e instanceof ServiceException) {
                throw e;
            }
            throw new ServiceException("处理文件上传完成失败: " + e.getMessage());
        }
    }



    /**
     * 根据文件夹路径查找对应的FolderInfo记录
     *
     * 优先根据folderPath查找，如果找不到则根据taskId和folderName查找。
     * 如果都找不到，说明批量任务创建时存在问题，应该抛出异常。
     * 支持多级文件夹路径映射。
     *
     * @param taskId 任务ID
     * @param folderPath 完整文件夹路径
     * @param folderName 文件夹名称
     * @param task 任务信息
     * @param issuePlace 签发地
     * @return FolderInfo的ID
     * @throws ServiceException 如果找不到对应的FolderInfo记录
     */
    private String findFolderInfoByPath(String taskId, String folderPath, String folderName,
                                       BatchUploadTask task, String issuePlace) {
        try {
            // 1. 优先根据folderPath查找
            if (StringUtils.hasText(folderPath)) {
                FolderInfo folderInfo = folderInfoRepository.findByFolderPath(folderPath);
                if (folderInfo != null) {
                    log.debug("根据folderPath找到FolderInfo: {}", folderPath);
                    return folderInfo.getFolderId();
                }
            }

            // 2. 根据taskId和folderName查找
            if (StringUtils.hasText(folderName)) {
                List<FolderInfo> folderInfos = folderInfoRepository.findByTaskIdAndFolderName(taskId, folderName);
                if (!folderInfos.isEmpty()) {
                    log.debug("根据taskId和folderName找到FolderInfo: {}", folderName);
                    return folderInfos.get(0).getFolderId();
                }
            }

            // 3. 如果都找不到，这是异常情况，应该抛出异常
            log.error("未找到匹配的FolderInfo记录，这表示批量任务创建时存在问题。taskId: {}, folderPath: {}, folderName: {}", 
                taskId, folderPath, folderName);
            throw new ServiceException("未找到对应的文件夹信息记录。请检查批量任务是否正确创建，或联系管理员。");

        } catch (Exception e) {
            log.error("查找FolderInfo失败，taskId: {}, folderPath: {}, folderName: {}",
                    taskId, folderPath, folderName, e);
            if (e instanceof ServiceException) {
                throw e;
            }
            throw new ServiceException("查找文件夹信息失败: " + e.getMessage());
        }
    }

    /**
     * 检查并设置主图路径
     *
     * 当上传的文件名为"可见光主图.jpg"时，将其路径设置到对应的FolderInfo记录中
     *
     * @param folderId 文件夹ID
     * @param originalFileName 原始文件名
     * @param finalMinioPath 文件在MinIO中的最终存储路径
     */
    private void checkAndSetMainPicture(String folderId, String originalFileName, String finalMinioPath) {
        try {
            // 检查是否为主图文件
            if ("可见光主图.jpg".equals(originalFileName)) {
                log.info("检测到主图文件，开始设置主图路径: folderId={}, fileName={}, path={}",
                    folderId, originalFileName, finalMinioPath);

                // 查找对应的FolderInfo记录
                FolderInfo folder = folderInfoRepository.findByFolderId(folderId);
                if (folder != null) {
                    folder.setMainPicPath(finalMinioPath);
                    folder.setUpdateTime(new Date());
                    folderInfoRepository.save(folder);

                    log.info("主图路径设置成功: folderId={}, mainPicPath={}", folderId, finalMinioPath);
                } else {
                    log.error("未找到对应的FolderInfo记录: folderId={}", folderId);
                    throw new ServiceException("未找到对应的文件夹记录: " + folderId);
                }
            }
        } catch (Exception e) {
            log.error("设置主图路径失败: folderId={}, fileName={}", folderId, originalFileName, e);
            throw new ServiceException("设置主图路径失败: " + e.getMessage());
        }
    }





    /**
     * 创建ImageRepository记录（使用原始文件名）
     *
     * 为每个上传的文件创建一条ImageRepository记录，记录文件的基本信息和MinIO路径。
     * 使用原始文件名而不是生成的UUID文件名，保持文件名的可读性。
     *
     * @param task 任务信息，提供国家、证件类型等上下文
     * @param folderId 文件夹ID，关联到FolderInfo
     * @param originalFileName 用户上传的原始文件名
     * @param finalMinioPath 文件在MinIO中的最终存储路径
     */
    private void createImageRepositoryWithOriginalName(BatchUploadTask task, String folderId,
                                                      String originalFileName, String finalMinioPath, String folderName) {
        try {
            // 创建新的ImageRepository对象
            ImageRepository imageRepo = new ImageRepository();

            // 设置基本信息
            imageRepo.setImageId(UUID.randomUUID().toString());
            imageRepo.setFolderId(folderId);
            imageRepo.setTaskId(task.getTaskId());

            // 使用原始文件名而不是生成的UUID文件名
            imageRepo.setFileName(originalFileName);
            imageRepo.setFilePath(finalMinioPath);
            imageRepo.setMinioPath(finalMinioPath); // 设置MinIO路径，用于删除时查找

            // 设置文件状态和时间
            imageRepo.setProcessStatus("UPLOADED");
            imageRepo.setCreateTime(new Date());
            imageRepo.setUpdateTime(new Date());

            // 自动检测图片类型并设置是否可标注
            String imageType = com.ruoyi.system.utils.ImageTypeDetector.detectImageType(originalFileName);
            boolean isAnnotatableType = com.ruoyi.system.utils.ImageTypeDetector.isAnnotatableType(imageType);
            imageRepo.setImageType(imageType);
            imageRepo.setIsAnnotatableType(isAnnotatableType);

            // 设置其他必要字段
            // 注意：业务元数据（国家、证件类型、签发年份等）现在存储在FolderInfo中
            // ImageRepository不再直接存储这些业务元数据，而是通过folderId关联到FolderInfo
            // 如果需要获取业务元数据，应该通过folderId查询对应的FolderInfo记录

            // 保存到数据库
            imageRepositoryRepo.save(imageRepo);
            log.info("ImageRepository记录创建成功，imageId: {}, fileName: {}, filePath: {}, taskId: {}, folderId: {}",
                    imageRepo.getImageId(), originalFileName, finalMinioPath, task.getTaskId(), folderId);

        } catch (Exception e) {
            log.error("创建ImageRepository记录失败，folderId: {}, fileName: {}, filePath: {}",
                    folderId, originalFileName, finalMinioPath, e);
            throw new ServiceException("创建图片记录失败: " + e.getMessage());
        }
    }

    /**
     * 创建ImageRepository记录
     *
     * 创建一个新的ImageRepository对象，填充所有必要字段，
     * 包括从BatchUploadTask继承的元数据和文件相关信息。
     *
     * @param task 任务信息，提供国家、证件类型等上下文
     * @param folderId 文件夹ID，关联到FolderInfo
     * @param originalFileName 用户上传的原始文件名
     * @param finalMinioPath 文件在MinIO中的最终存储路径
     */
    private void createImageRepository(BatchUploadTask task, String folderId,
                                     String originalFileName, String finalMinioPath) {
        try {
            // 创建新的ImageRepository对象
            ImageRepository imageRepo = new ImageRepository();

            // imageId: 使用UUID生成
            imageRepo.setImageId(UUID.randomUUID().toString());

            // folderId: 设置为上一步获取到的folderId
            imageRepo.setFolderId(folderId);

            // taskId: 关联任务
            imageRepo.setTaskId(task.getTaskId());

            // minioPath: 使用传入的finalMinioPath参数
            imageRepo.setMinioPath(finalMinioPath);

            // fileName: 使用FilenameUtils.getName从路径中解析出文件名
            imageRepo.setFileName(FilenameUtils.getName(finalMinioPath));

            // originalFileName: 使用传入的originalFileName参数
            imageRepo.setOriginalFileName(originalFileName);

            // lightType: 设置为null（后续手动设置）
            imageRepo.setLightType(null);

            // isMainImage: 设置为false（后续手动设置）
            imageRepo.setIsMainImage(false);

            // annotations: 设置为空的ArrayList
            imageRepo.setAnnotations(new ArrayList<>());

            // 自动检测图片类型并设置是否可标注
            String imageType = com.ruoyi.system.utils.ImageTypeDetector.detectImageType(originalFileName);
            boolean isAnnotatableType = com.ruoyi.system.utils.ImageTypeDetector.isAnnotatableType(imageType);
            imageRepo.setImageType(imageType);
            imageRepo.setIsAnnotatableType(isAnnotatableType);

            // 注意：业务元数据现在存储在FolderInfo中，不再从task继承
            // 如果需要设置业务元数据，应该通过folderId查询对应的FolderInfo记录

            // 设置时间戳
            imageRepo.setCreateTime(new Date());
            imageRepo.setUpdateTime(new Date());

            // 设置处理状态
            imageRepo.setProcessStatus("uploaded");

            // 使用imageRepositoryRepo.save()保存实体
            ImageRepository savedImage = imageRepositoryRepo.save(imageRepo);

            log.debug("ImageRepository记录创建成功，imageId: {}, fileName: {}",
                    savedImage.getImageId(), savedImage.getFileName());

        } catch (Exception e) {
            log.error("创建ImageRepository记录失败，taskId: {}, 文件: {}",
                    task.getTaskId(), originalFileName, e);
            throw new ServiceException("创建图片记录失败: " + e.getMessage());
        }
    }

    /**
     * 更新任务进度
     *
     * 使用MongoTemplate原子性地更新BatchUploadTask的processedFiles计数，
     * 并检查任务是否完成。如果完成则更新状态为"COMPLETED"并设置结束时间。
     *
     * @param taskId 任务ID，用于定位要更新的任务
     */
    private void updateTaskProgress(String taskId, String folderId) {
        try {
            // 1. 更新BatchUploadTask进度
            Query taskQuery = new Query(Criteria.where("taskId").is(taskId));
            Update taskUpdate = new Update()
                    .inc("processedFiles", 1)
                    .set("updateTime", new Date());
            mongoTemplate.updateFirst(taskQuery, taskUpdate, BatchUploadTask.class);

            // 2. 同时更新对应FolderInfo的uploadedFileCount
            Query folderQuery = new Query(Criteria.where("folderId").is(folderId));
            Update folderUpdate = new Update()
                    .inc("uploadedFileCount", 1)
                    .set("updateTime", new Date());
            mongoTemplate.updateFirst(folderQuery, folderUpdate, FolderInfo.class);

            log.info("任务和文件夹进度更新完成，taskId: {}, folderId: {}", taskId, folderId);

            // 检查任务是否完成 - 再次查询更新后的BatchUploadTask
            Optional<BatchUploadTask> updatedTaskOpt = batchUploadTaskRepository.findByTaskId(taskId);
            if (updatedTaskOpt.isPresent()) {
                BatchUploadTask updatedTask = updatedTaskOpt.get();

                // 记录当前进度
                log.info("任务进度更新: taskId={}, 当前进度={}/{}, 任务名称={}",
                    taskId, updatedTask.getProcessedFiles(), updatedTask.getTotalFiles(), updatedTask.getTaskName());

                // 如果processedFiles >= totalFiles，则任务完成
                if (updatedTask.getProcessedFiles() >= updatedTask.getTotalFiles()) {
                    log.info("任务完成检测：processedFiles={}, totalFiles={}",
                            updatedTask.getProcessedFiles(), updatedTask.getTotalFiles());

                    // 将BatchUploadTask的status更新为"COMPLETED"，并设置endTime
                    Query completionQuery = new Query(Criteria.where("taskId").is(taskId));
                    Update completionUpdate = new Update()
                            .set("status", "COMPLETED")
                            .set("endTime", new Date())
                            .set("updateTime", new Date());

                    // 执行最终状态更新
                    mongoTemplate.updateFirst(completionQuery, completionUpdate, BatchUploadTask.class);

                    // 🔥 关键修复：同时更新该任务下所有FolderInfo的状态从PROCESSING到unassociated
                    updateFolderStatusOnTaskCompletion(taskId);

                    // 🚀 新增：触发异步版本解析（可配置是否启用）
                    triggerVersionParsingAsync(taskId);

                    log.info("任务已完成，taskId: {}, 任务名称: {}", taskId, updatedTask.getTaskName());
                } else {
                    log.debug("任务进行中，taskId: {}, 进度: {}/{}",
                            taskId, updatedTask.getProcessedFiles(), updatedTask.getTotalFiles());
                }
            } else {
                log.warn("更新进度后无法查询到任务，taskId: {}", taskId);
            }

        } catch (Exception e) {
            log.error("更新任务进度失败，taskId: {}", taskId, e);
            throw new ServiceException("更新任务进度失败: " + e.getMessage());
        }
    }

    /**
     * 任务完成时更新文件夹状态
     *
     * 当BatchUploadTask完成时，将该任务下所有FolderInfo的状态从PROCESSING更新为unassociated，
     * 使文件夹可以进行版本关联操作。
     *
     * @param taskId 任务ID
     */
    private void updateFolderStatusOnTaskCompletion(String taskId) {
        try {
            log.info("任务完成，开始更新文件夹状态，taskId: {}", taskId);

            // 1. 首先验证所有文件夹都有主图
            validateMainPicturesForTask(taskId);

            // 2. 查询该任务下所有状态为PROCESSING的文件夹
            Query folderQuery = new Query(
                Criteria.where("taskId").is(taskId)
                        .and("status").is("PROCESSING")
            );

            // 3. 更新状态为unassociated，表示可以进行版本关联
            Update folderUpdate = new Update()
                    .set("status", "unassociated")
                    .set("updateTime", new Date());

            // 4. 执行批量更新
            UpdateResult updateResult = mongoTemplate.updateMulti(folderQuery, folderUpdate, FolderInfo.class);

            log.info("任务完成，成功更新{}个文件夹状态：PROCESSING → unassociated，taskId: {}",
                    updateResult.getModifiedCount(), taskId);

        } catch (Exception e) {
            log.error("更新文件夹状态失败，taskId: {}", taskId, e);
        }
    }

    /**
     * 验证任务下所有文件夹都包含主图
     *
     * 在任务完成时调用，确保每个文件夹都有"可见光主图.jpg"文件
     * 如果有文件夹缺少主图，将任务状态设置为失败并记录错误信息
     *
     * @param taskId 任务ID
     * @throws ServiceException 当有文件夹缺少主图时抛出异常
     */
    private void validateMainPicturesForTask(String taskId) {
        try {
            log.info("开始验证任务下所有文件夹的主图，taskId: {}", taskId);

            // 查询该任务下所有文件夹
            Query folderQuery = new Query(Criteria.where("taskId").is(taskId));
            List<FolderInfo> folders = mongoTemplate.find(folderQuery, FolderInfo.class);

            List<String> foldersWithoutMainPic = new ArrayList<>();

            // 检查每个文件夹是否有主图路径
            for (FolderInfo folder : folders) {
                if (folder.getMainPicPath() == null || folder.getMainPicPath().trim().isEmpty()) {
                    foldersWithoutMainPic.add(folder.getFolderName());
                    log.warn("文件夹缺少主图: folderId={}, folderName={}",
                        folder.getFolderId(), folder.getFolderName());
                }
            }

            // 如果有文件夹缺少主图，记录错误并抛出异常
            if (!foldersWithoutMainPic.isEmpty()) {
                String errorMessage = String.format("以下文件夹缺少必需的文件'可见光主图.jpg'：%s",
                    String.join(", ", foldersWithoutMainPic));

                log.error("任务验证失败: {}, taskId: {}", errorMessage, taskId);

                // 更新任务状态为失败
                failTask(taskId, errorMessage);

                throw new ServiceException(errorMessage);
            }

            log.info("主图验证通过，所有{}个文件夹都包含主图，taskId: {}", folders.size(), taskId);

        } catch (Exception e) {
            log.error("验证主图时发生异常，taskId: {}", taskId, e);
            if (e instanceof ServiceException) {
                throw e;
            }
            throw new ServiceException("验证主图失败: " + e.getMessage());
        }
    }

    /**
     * 触发版本解析
     *
     * 当任务完成时，触发异步版本解析，解析文件夹名称并生成版本信息。
     * 解析过程不会阻塞主线程，确保用户体验流畅。
     *
     * @param taskId 任务ID
     */
    private void triggerVersionParsing(String taskId) {
        try {
            log.info("任务完成，开始触发版本解析，taskId: {}", taskId);

            // 异步触发版本解析
            versionNameParseService.parseAllFoldersAsync(taskId);

            log.info("版本解析已触发，taskId: {}", taskId);
        } catch (Exception e) {
            log.error("触发版本解析失败，taskId: {}", taskId, e);
            // 解析失败不影响主流程，只记录错误日志
        }
    }

    @Override
    public boolean checkTaskCompletionAndTriggerParsing(String taskId) {
        try {
            log.info("检查任务完成状态并触发解析，taskId: {}", taskId);

            // 查询任务信息
            Optional<BatchUploadTask> taskOpt = batchUploadTaskRepository.findByTaskId(taskId);
            if (!taskOpt.isPresent()) {
                log.warn("任务不存在，taskId: {}", taskId);
                return false;
            }

            BatchUploadTask task = taskOpt.get();

            // 检查任务是否已完成
            if (task.getProcessedFiles() >= task.getTotalFiles()) {
                log.info("任务已完成，触发后续处理，taskId: {}", taskId);

                // 更新任务状态为COMPLETED
                if (!"COMPLETED".equals(task.getStatus())) {
                    Query query = new Query(Criteria.where("taskId").is(taskId));
                    Update update = new Update()
                            .set("status", "COMPLETED")
                            .set("endTime", new Date())
                            .set("updateTime", new Date());
                    mongoTemplate.updateFirst(query, update, BatchUploadTask.class);
                }

                // 更新文件夹状态
                updateFolderStatusOnTaskCompletion(taskId);

                // 触发版本解析
                triggerVersionParsing(taskId);

                return true;
            } else {
                log.debug("任务尚未完成，processedFiles: {}, totalFiles: {}",
                         task.getProcessedFiles(), task.getTotalFiles());
                return false;
            }

        } catch (Exception e) {
            log.error("检查任务完成状态失败，taskId: {}", taskId, e);
            return false;
        }
    }

    /**
     * 级联删除批量上传任务
     *
     * 删除指定的批量上传任务及其所有关联数据，包括：
     * 1. FolderInfo 记录（会自动级联删除 ImageRepository 记录和 MinIO 文件）
     * 2. BatchUploadTask 记录
     *
     * @param taskId 任务ID
     * @return 是否删除成功
     */
    @Override
    @Transactional
    public boolean deleteBatchTaskWithCascade(String taskId) {
        try {
            log.info("开始级联删除批量任务，taskId: {}", taskId);

            // 1. 检查任务是否存在
            Optional<BatchUploadTask> taskOpt = batchUploadTaskRepository.findByTaskId(taskId);
            if (!taskOpt.isPresent()) {
                log.warn("要删除的任务不存在，taskId: {}", taskId);
                return false;
            }

            BatchUploadTask task = taskOpt.get();
            log.info("找到任务: {}，开始级联删除", task.getTaskName());

            // 2. 删除关联的文件夹数据（会自动级联删除图片记录和MinIO文件）
            int deletedFolderCount = folderInfoService.deleteFoldersByTaskId(taskId);
            log.info("删除了{}个文件夹及其关联数据", deletedFolderCount);

            // 3. 删除任务记录
            batchUploadTaskRepository.deleteByTaskId(taskId);
            log.info("删除任务记录成功，taskId: {}", taskId);

            log.info("批量任务级联删除完成，taskId: {}", taskId);
            return true;

        } catch (Exception e) {
            log.error("级联删除批量任务失败，taskId: {}", taskId, e);
            return false;
        }
    }





    /**
     * 更新任务状态 (私有方法)
     */
    private boolean updateTaskStatusInternal(String taskId, String status) {
        try {
            Query query = new Query(Criteria.where("taskId").is(taskId));
            Update update = new Update()
                    .set("status", status)
                    .set("updateTime", new Date());

            if ("COMPLETED".equals(status)) {
                update.set("endTime", new Date());
            }

            boolean result = mongoTemplate.updateFirst(query, update, BatchUploadTask.class).getModifiedCount() > 0;
            log.info("更新任务状态成功，taskId: {}, status: {}", taskId, status);
            return result;

        } catch (Exception e) {
            log.error("更新任务状态失败，taskId: {}, status: {}", taskId, status, e);
            return false;
        }
    }

    /**
     * 级联删除多版本批量上传任务
     */
    @Override
    @Transactional
    public boolean deleteMultiVersionBatchTaskWithCascade(String taskId) {
        try {
            log.info("开始级联删除多版本批量任务，taskId: {}", taskId);

            // 1. 检查任务是否存在
            Optional<BatchUploadTask> taskOpt = batchUploadTaskRepository.findByTaskId(taskId);
            if (!taskOpt.isPresent()) {
                log.warn("要删除的任务不存在，taskId: {}", taskId);
                return false;
            }

            BatchUploadTask task = taskOpt.get();
            log.info("找到任务: {}，开始级联删除", task.getTaskName());

            // 2. 删除任务关联的所有FolderInfo和ImageRepository
            int deletedFolderCount = folderInfoService.deleteFoldersByTaskId(taskId);
            log.info("删除了{}个文件夹及其关联数据", deletedFolderCount);

            // 3. 删除任务记录
            batchUploadTaskRepository.deleteByTaskId(taskId);
            log.info("删除任务记录成功，taskId: {}", taskId);

            log.info("批量任务级联删除完成，taskId: {}, 删除文件夹: {}",
                    taskId, deletedFolderCount);
            return true;

        } catch (Exception e) {
            log.error("级联删除批量任务失败，taskId: {}", taskId, e);
            return false;
        }
    }

    /**
     * 生成任务名称
     * 格式：YYYYMMDD_部门名称_多版本批量上传任务_HHMM
     */
    private String generateTaskName(Long deptId, Integer versionCount) {
        try {
            // 获取当前时间
            Date now = new Date();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
            SimpleDateFormat timeFormat = new SimpleDateFormat("HHmm");
            String dateStr = dateFormat.format(now);
            String timeStr = timeFormat.format(now);

            // 根据deptId查询实际部门名称
            String deptName = "未知部门"; // 默认值
            try {
                if (deptId != null) {
                    SysDept dept = sysDeptService.selectDeptById(deptId);
                    if (dept != null && StringUtils.isNotEmpty(dept.getDeptName())) {
                        deptName = dept.getDeptName();
                    }
                }
            } catch (Exception e) {
                log.warn("查询部门名称失败，deptId: {}, 使用默认名称", deptId, e);
            }

            // 生成任务名称
            String taskName = String.format("%s_%s_多版本批量上传任务_%s",
                dateStr, deptName, timeStr);

            log.debug("生成任务名称: {}", taskName);
            return taskName;

        } catch (Exception e) {
            log.error("生成任务名称失败", e);
            // 如果生成失败，使用默认格式
            return "多版本批量上传任务_" + System.currentTimeMillis();
        }
    }

    /**
     * 异步触发版本解析
     * 将版本解析任务提交到线程池中异步执行，避免阻塞文件上传流程
     */
    private void triggerVersionParsingAsync(String taskId) {
        // 使用 @Async 注解或者线程池来异步执行
        // 这里简化处理，直接在新线程中执行
        new Thread(() -> {
            try {
                Thread.sleep(1000); // 等待1秒，确保所有文件上传完成
                log.info("开始异步处理任务版本解析，taskId: {}", taskId);

                // 这里可以添加版本解析、图片分析等耗时操作
                // 例如：
                // - 图片质量检测
                // - 文件格式验证
                // - 自动分类
                // - 缩略图生成
                // - OCR文字识别

                log.info("任务版本解析完成，taskId: {}", taskId);
            } catch (Exception e) {
                log.error("异步版本解析失败，taskId: {}", taskId, e);
            }
        }, "version-parsing-" + taskId).start();
    }

    /**
     * 检查图片是否已存在，避免重复处理
     */
    private boolean isImageAlreadyExists(String folderId, String fileName, String filePath) {
        try {
            // 只根据 folderId 和 fileName 查询，因为同一文件夹下文件名应该唯一
            Query query = new Query();
            query.addCriteria(Criteria.where("folderId").is(folderId)
                .and("fileName").is(fileName));

            long count = mongoTemplate.count(query, ImageRepository.class);
            boolean exists = count > 0;

            if (exists) {
                log.info("检测到重复文件: folderId={}, fileName={}, 跳过处理", folderId, fileName);
            }

            return exists;
        } catch (Exception e) {
            log.error("检查图片是否存在失败: folderId={}, fileName={}", folderId, fileName, e);
            return false; // 出错时允许继续处理
        }
    }

    // ==================== 以下方法来自原 BatchUploadTaskServiceImpl (流程编排功能) ====================

    @Override
    public BatchUploadTask createTask(BatchUploadTask task) {
        try {
            task.setCreateTime(new Date());
            if (task.getStatus() == null) {
                task.setStatus("CREATED");
            }
            return mongoTemplate.save(task);
        } catch (Exception e) {
            log.error("创建批量上传任务失败: {}", e.getMessage(), e);
            throw new RuntimeException("创建任务失败", e);
        }
    }

    @Override
    public BatchUploadTaskVO getTaskById(String taskId) {
        try {
            Query query = new Query(Criteria.where("taskId").is(taskId));
            BatchUploadTask task = mongoTemplate.findOne(query, BatchUploadTask.class);
            return convertToVO(task);
        } catch (Exception e) {
            log.error("根据任务ID获取任务失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public BatchUploadTask getTaskByMongoId(String id) {
        try {
            return mongoTemplate.findById(id, BatchUploadTask.class);
        } catch (Exception e) {
            log.error("根据MongoDB主键获取任务失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean updateTaskStatus(String taskId, String status) {
        try {
            Query query = new Query(Criteria.where("taskId").is(taskId));
            Update update = new Update()
                    .set("status", status)
                    .set("updateTime", new Date());

            return mongoTemplate.updateFirst(query, update, BatchUploadTask.class).getModifiedCount() > 0;
        } catch (Exception e) {
            log.error("更新任务状态失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean updateTaskProgress(String taskId, Integer processedFolders, Integer processedFiles) {
        try {
            Query query = new Query(Criteria.where("taskId").is(taskId));
            Update update = new Update()
                    .set("processedFolders", processedFolders)
                    .set("processedFiles", processedFiles)
                    .set("updateTime", new Date());

            return mongoTemplate.updateFirst(query, update, BatchUploadTask.class).getModifiedCount() > 0;
        } catch (Exception e) {
            log.error("更新任务进度失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean updateTaskProgressAndStatus(String taskId, Integer processedFolders, Integer processedFiles, String status) {
        try {
            Query query = new Query(Criteria.where("taskId").is(taskId));
            Update update = new Update()
                    .set("processedFolders", processedFolders)
                    .set("processedFiles", processedFiles)
                    .set("status", status)
                    .set("updateTime", new Date());

            return mongoTemplate.updateFirst(query, update, BatchUploadTask.class).getModifiedCount() > 0;
        } catch (Exception e) {
            log.error("原子性更新任务进度和状态失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean completeTask(String taskId) {
        return updateTaskStatusInternal(taskId, "COMPLETED");
    }

    @Override
    public boolean failTask(String taskId, String errorMessage) {
        try {
            Query query = new Query(Criteria.where("taskId").is(taskId));
            Update update = new Update()
                    .set("status", "FAILED")
                    .set("errorMessage", errorMessage)
                    .set("updateTime", new Date());

            return mongoTemplate.updateFirst(query, update, BatchUploadTask.class).getModifiedCount() > 0;
        } catch (Exception e) {
            log.error("标记任务失败失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<BatchUploadTaskVO> getTasksByUserId(Long userId) {
        try {
            // 注意：BatchUploadTask实体中没有userId字段，只有createdBy(String)字段
            // 这里需要根据实际业务需求调整查询逻辑
            // 暂时使用deptId字段进行查询，如果需要按用户查询，需要先获取用户名
            Query query = new Query(Criteria.where("deptId").is(userId));
            List<BatchUploadTask> tasks = mongoTemplate.find(query, BatchUploadTask.class);
            return convertToVOList(tasks);
        } catch (Exception e) {
            log.error("根据用户ID获取任务列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<BatchUploadTaskVO> getTasksByDeptId(Long deptId) {
        try {
            Query query = new Query(Criteria.where("deptId").is(deptId));
            List<BatchUploadTask> tasks = mongoTemplate.find(query, BatchUploadTask.class);
            return convertToVOList(tasks);
        } catch (Exception e) {
            log.error("根据部门ID获取任务列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<BatchUploadTaskVO> getTasksByStatus(String status) {
        try {
            Query query = new Query(Criteria.where("status").is(status));
            List<BatchUploadTask> tasks = mongoTemplate.find(query, BatchUploadTask.class);
            return convertToVOList(tasks);
        } catch (Exception e) {
            log.error("根据状态获取任务列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public boolean deleteTask(String taskId) {
        try {
            // 先删除关联的文件夹数据
            folderInfoService.deleteFoldersByTaskId(taskId);

            // 再删除任务记录
            Query query = new Query(Criteria.where("taskId").is(taskId));
            return mongoTemplate.remove(query, BatchUploadTask.class).getDeletedCount() > 0;
        } catch (Exception e) {
            log.error("删除任务失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean startBatchUploadProcess(String taskId, List<String> folderPaths) {
        try {
            log.info("启动批量上传流程: taskId={}, folderCount={}", taskId, folderPaths.size());

            // 更新任务状态为处理中
            updateTaskStatus(taskId, "PROCESSING");

            // 处理每个文件夹
            for (String folderPath : folderPaths) {
                if (!processSingleFolder(taskId, folderPath)) {
                    log.error("处理文件夹失败: {}", folderPath);
                    failTask(taskId, "处理文件夹失败: " + folderPath);
                    return false;
                }
            }

            log.info("批量上传流程启动成功: taskId={}", taskId);
            return true;
        } catch (Exception e) {
            log.error("启动批量上传流程失败: {}", e.getMessage(), e);
            failTask(taskId, "启动批量上传流程失败: " + e.getMessage());
            return false;
        }
    }

    @Override
    public boolean processSingleFolder(String taskId, String folderPath) {
        try {
            log.info("处理单个文件夹: taskId={}, folderPath={}", taskId, folderPath);

            // 这里应该调用文件夹处理服务
            // 暂时返回true，具体实现需要根据业务需求调整
            // TODO: 实现具体的文件夹处理逻辑
            return true;
        } catch (Exception e) {
            log.error("处理单个文件夹失败: taskId={}, folderPath={}", taskId, folderPath, e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getTaskStatistics(String taskId) {
        try {
            Map<String, Object> stats = new HashMap<>();

            // 获取任务基本信息
            Query taskQuery = new Query(Criteria.where("taskId").is(taskId));
            BatchUploadTask task = mongoTemplate.findOne(taskQuery, BatchUploadTask.class);

            if (task == null) {
                return stats;
            }

            stats.put("taskId", taskId);
            stats.put("status", task.getStatus());
            stats.put("totalFolders", task.getTotalFolders());
            stats.put("processedFolders", task.getProcessedFolders());
            stats.put("totalFiles", task.getTotalFiles());
            stats.put("processedFiles", task.getProcessedFiles());
            stats.put("createTime", task.getCreateTime());
            stats.put("updateTime", task.getUpdateTime());

            // 计算进度百分比
            if (task.getTotalFiles() != null && task.getTotalFiles() > 0) {
                double progress = (double) task.getProcessedFiles() / task.getTotalFiles() * 100;
                stats.put("progressPercentage", Math.round(progress * 100.0) / 100.0);
            } else {
                stats.put("progressPercentage", 0.0);
            }

            return stats;
        } catch (Exception e) {
            log.error("获取任务统计信息失败: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }

    @Override
    public boolean retryTask(String taskId) {
        try {
            log.info("重试任务: {}", taskId);

            // 重置任务状态为创建状态
            Query query = new Query(Criteria.where("taskId").is(taskId));
            Update update = new Update()
                    .set("status", "CREATED")
                    .set("processedFolders", 0)
                    .set("processedFiles", 0)
                    .unset("errorMessage")
                    .set("updateTime", new Date());

            return mongoTemplate.updateFirst(query, update, BatchUploadTask.class).getModifiedCount() > 0;
        } catch (Exception e) {
            log.error("重试任务失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean pauseTask(String taskId) {
        return updateTaskStatusInternal(taskId, "PAUSED");
    }

    @Override
    public boolean resumeTask(String taskId) {
        return updateTaskStatusInternal(taskId, "PROCESSING");
    }

    /**
     * 转换为VO对象
     */
    private BatchUploadTaskVO convertToVO(BatchUploadTask task) {
        if (task == null) {
            return null;
        }

        BatchUploadTaskVO vo = new BatchUploadTaskVO();
        vo.setId(task.getId());
        vo.setTaskId(task.getTaskId());
        vo.setStatus(task.getStatus());
        vo.setTotalFolders(task.getTotalFolders());
        vo.setProcessedFolders(task.getProcessedFolders());
        vo.setTotalFiles(task.getTotalFiles());
        vo.setProcessedFiles(task.getProcessedFiles());

        // 转换Date到LocalDateTime
        if (task.getCreateTime() != null) {
            vo.setCreateTime(task.getCreateTime().toInstant()
                    .atZone(java.time.ZoneId.systemDefault())
                    .toLocalDateTime());
        }
        if (task.getStartTime() != null) {
            vo.setStartTime(task.getStartTime().toInstant()
                    .atZone(java.time.ZoneId.systemDefault())
                    .toLocalDateTime());
        }
        if (task.getEndTime() != null) {
            vo.setEndTime(task.getEndTime().toInstant()
                    .atZone(java.time.ZoneId.systemDefault())
                    .toLocalDateTime());
        }

        return vo;
    }

    /**
     * 转换为VO列表
     */
    private List<BatchUploadTaskVO> convertToVOList(List<BatchUploadTask> tasks) {
        if (tasks == null || tasks.isEmpty()) {
            return new ArrayList<>();
        }

        return tasks.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }
}
