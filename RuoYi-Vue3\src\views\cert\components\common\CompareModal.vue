<template>
  <el-dialog
    v-model="dialogVisible"
    title="文件夹与版本比对"
    width="900px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <!-- 主体内容：两栏布局 -->
    <div class="compare-container">
      <!-- 左栏：待处理文件夹 -->
      <div class="left-panel">
        <h3 class="panel-title">待处理文件夹</h3>
        <div class="image-container">
          <el-image
            v-if="folderToCompare?.mainPicPath"
            :src="getMainPicUrl(folderToCompare.mainPicPath)"
            :preview-src-list="[getMainPicUrl(folderToCompare.mainPicPath)]"
            fit="contain"
            class="compare-image"
          >
            <template #error>
              <div class="image-error">
                <el-icon><Picture /></el-icon>
                <span>图片加载失败</span>
              </div>
            </template>
          </el-image>
          <div v-else class="image-placeholder">
            <el-icon><Picture /></el-icon>
            <span>暂无主图</span>
          </div>
        </div>
        <div class="folder-info">
          <p class="folder-name">{{ folderToCompare?.folderName || '未知文件夹' }}</p>
        </div>
      </div>

      <!-- 右栏：目标版本 -->
      <div class="right-panel">
        <h3 class="panel-title">选择目标版本进行比对</h3>
        
        <!-- 版本选择器 -->
        <div class="version-selector-wrapper">
          <VersionSelector @version-selected="handleVersionSelected" />
        </div>
        
        <!-- 版本图片显示区域 -->
        <div class="version-image-container">
          <el-image
            v-if="selectedVersion?.trainingImage?.url"
            :src="selectedVersion.trainingImage.url"
            :preview-src-list="[selectedVersion.trainingImage.url]"
            fit="contain"
            class="compare-image"
          >
            <template #error>
              <div class="image-error">
                <el-icon><Picture /></el-icon>
                <span>图片加载失败</span>
              </div>
            </template>
          </el-image>
          <div v-else class="image-placeholder">
            <el-icon><Picture /></el-icon>
            <span>请先选择一个版本</span>
          </div>
        </div>
        
        <!-- 版本信息 -->
        <div v-if="selectedVersion" class="version-info">
          <p class="version-code">{{ selectedVersion.versionCode }}</p>
          <p class="version-description">{{ selectedVersion.description || '暂无描述' }}</p>
        </div>
      </div>
    </div>

    <!-- 底部操作区 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :disabled="!selectedVersion"
          @click="handleAssociateConfirm"
        >
          确认关联
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Picture } from '@element-plus/icons-vue'

// 导入版本选择器组件
import VersionSelector from '@/views/cert/version/components/VersionSelector.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  folderToCompare: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'associate-confirm'])

// 响应式数据
const dialogVisible = ref(false)
const selectedVersion = ref(null)

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    // 弹窗打开时重置选择状态
    selectedVersion.value = null
    // 调试信息
    console.log('CompareModal 打开，folderToCompare:', props.folderToCompare)
    console.log('mainPicPath:', props.folderToCompare?.mainPicPath)
  }
})

// 监听 dialogVisible 变化
watch(dialogVisible, (newVal) => {
  if (!newVal) {
    emit('update:modelValue', false)
  }
})

// 处理版本选择
const handleVersionSelected = (version) => {
  selectedVersion.value = version
  console.log('选择了版本:', version)
}

// 处理确认关联
const handleAssociateConfirm = () => {
  if (!selectedVersion.value) {
    ElMessage.warning('请先选择一个版本')
    return
  }
  
  // 触发关联确认事件，传递文件夹和版本信息
  emit('associate-confirm', props.folderToCompare, selectedVersion.value)
  
  // 关闭弹窗
  handleClose()
}

// 处理关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  selectedVersion.value = null
}

// 构建主图URL
const getMainPicUrl = (mainPicPath) => {
  if (!mainPicPath) {
    return ''
  }

  console.log('构建主图URL，mainPicPath:', mainPicPath)

  // 如果已经是完整URL，直接返回
  if (mainPicPath.startsWith('http://') || mainPicPath.startsWith('https://')) {
    return mainPicPath
  }

  // 构建MinIO访问URL
  // 方案1: 直接访问MinIO（如果MinIO端口可访问）
  const directMinioUrl = `http://localhost:9000/xjlfiles/${mainPicPath}`

  // 方案2: 通过后端代理访问（推荐）
  const proxyUrl = `/dev-api/common/image/proxy?url=${encodeURIComponent(directMinioUrl)}`

  console.log('生成的主图URL:', proxyUrl)
  return proxyUrl
}
</script>

<style scoped lang="scss">
.compare-container {
  display: flex;
  gap: 20px;
  min-height: 400px;
}

.left-panel,
.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.panel-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.image-container,
.version-image-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  background-color: #fafafa;
  margin-bottom: 16px;
}

.compare-image {
  width: 100%;
  height: 280px;
  object-fit: contain;
  border-radius: 6px;
}

.image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 14px;
  
  .el-icon {
    font-size: 48px;
    margin-bottom: 8px;
  }
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #f56c6c;
  font-size: 14px;
  
  .el-icon {
    font-size: 48px;
    margin-bottom: 8px;
  }
}

.folder-info,
.version-info {
  text-align: center;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 6px;
}

.folder-name,
.version-code {
  margin: 0 0 4px 0;
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.version-description {
  margin: 0;
  color: #606266;
  font-size: 12px;
  line-height: 1.4;
}

.version-selector-wrapper {
  margin-bottom: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  background-color: #fafafa;
}

.dialog-footer {
  text-align: right;
}

// 响应式设计
@media (max-width: 768px) {
  .compare-container {
    flex-direction: column;
    gap: 16px;
  }
  
  .image-container,
  .version-image-container {
    min-height: 200px;
  }
  
  .compare-image {
    height: 180px;
  }
}
</style> 