# TaskWorkspaceView 比对功能重构指南

## 需求概述

将 TaskWorkspaceView 的比对功能从弹窗模式改为内嵌模式，实现以下功能：

- **左侧文件夹列表**：单选模式，选择要比对的文件夹
- **右侧版本浏览器**：多选模式，选择要比对的版本
- **比对区域**：内嵌显示，支持版本切换和直接关联
- **关联功能**：在比对区域直接进行关联操作

## 修改步骤

### 1. 修改左侧文件夹列表组件 (UnassignedFolderList)

```javascript
// 修改 UnassignedFolderList 组件，添加单选功能：
1. 添加 selectedFolderId 状态来跟踪当前选中的文件夹
2. 修改 FolderListItem 组件，添加单选模式（radio 样式）
3. 当用户点击文件夹时，更新选中状态并触发 selection-change 事件
4. 选中的文件夹应该有明显的视觉反馈（高亮边框、背景色等）
5. 确保只能选择一个文件夹，选择新文件夹时自动取消之前的选择
```

### 2. 修改右侧版本浏览器组件 (VersionBrowser)

```javascript
// 修改 VersionBrowser 组件，添加多选功能：
1. 添加 selectedVersionIds 数组来跟踪选中的版本列表
2. 修改版本列表项，添加复选框功能
3. 支持批量选择操作，可以同时选择多个版本
4. 选中的版本应该有明显的视觉反馈
5. 添加全选/取消全选功能
6. 显示当前选中的版本数量
```

### 3. 重构 CompareModal 组件为 CompareArea

```javascript
// 将 CompareModal 组件改造成比对区域组件：
1. 重命名组件为 CompareArea 或 ImageCompareArea
2. 移除弹窗相关的 el-dialog 包装
3. 改为普通的 div 容器，可以直接嵌入到页面中
4. 保持左右分栏的比对布局结构
5. 移除关闭按钮和弹窗相关的逻辑

// 修改组件的 Props 和 Emits：
1. Props 改为接收：
   - selectedFolder: 选中的文件夹对象
   - selectedVersions: 选中的版本数组
   - currentVersionIndex: 当前显示的版本索引
2. Emits 改为：
   - 'version-change': 版本切换事件
   - 'associate-folder': 关联文件夹事件
   - 'close-compare': 关闭比对区域事件
3. 移除 modelValue 相关的双向绑定
```

### 4. 添加版本切换功能

```javascript
// 在比对区域添加版本切换控制：
1. 当 selectedVersions 长度大于1时，显示版本切换控制
2. 添加左右箭头按钮，用于切换当前显示的版本
3. 显示当前版本索引信息（如：版本 2/5）
4. 在第一个版本时禁用左箭头，在最后一个版本时禁用右箭头
5. 切换版本时触发 'version-change' 事件
```

### 5. 添加关联功能

```javascript
// 在比对区域添加关联按钮：
1. 在比对区域底部添加"关联到当前版本"按钮
2. 按钮只在有选中文件夹和版本时显示
3. 点击按钮时触发 'associate-folder' 事件，传递：
   - 文件夹信息
   - 当前显示的版本信息
4. 按钮样式要醒目，使用 primary 类型
5. 添加确认提示（可选）
```

### 6. 优化图片显示

```javascript
// 优化图片显示功能：
1. 左侧显示选中文件夹的主图（mainPicPath）
2. 右侧显示当前选中版本的主图（mainPicPath）
3. 图片支持点击放大预览
4. 添加图片加载失败的处理和占位图
5. 图片容器设置合适的高度，确保两张图片对齐
6. 添加图片加载时的骨架屏效果
```

### 7. 更新 TaskWorkspaceView 主组件

```javascript
// 在 TaskWorkspaceView 中添加比对区域：
1. 当左侧有选中文件夹且右侧有选中版本时，显示比对区域
2. 比对区域采用左右分栏布局：
   - 左侧显示选中文件夹的主图（大图显示）
   - 右侧显示当前选中版本的主图（大图显示）
3. 如果右侧选择了多个版本，添加左右箭头按钮用于切换版本
4. 显示当前查看的版本索引（如：1/3）
5. 图片支持点击放大预览
6. 添加图片加载失败的处理

// 更新比对区域的使用：
1. 将 CompareModal 替换为新的 CompareArea 组件
2. 在主要内容区域添加比对区域的条件显示
3. 当左侧有选中文件夹且右侧有选中版本时，显示比对区域
4. 处理版本切换事件，更新 currentVersionIndex
5. 处理关联事件，调用关联 API
6. 关联成功后清空选择并刷新数据
```

### 8. 更新状态管理

```javascript
// 在 TaskWorkspaceView 中添加比对相关的状态：
1. selectedFolderId: 当前选中的文件夹ID
2. selectedVersionIds: 当前选中的版本ID数组
3. currentVersionIndex: 当前显示的版本索引
4. showCompareArea: 是否显示比对区域（computed）
5. 处理文件夹选择变化事件
6. 处理版本选择变化事件
7. 处理版本切换事件

// 更新相关的事件处理函数：
1. 更新 handleFolderSelectionChange 方法
2. 添加 handleVersionSelectionChange 方法
3. 添加 handleVersionChange 方法
4. 添加 handleAssociateFolder 方法
5. 确保状态变化时正确触发组件更新
```

### 9. 移除原有的 CompareModal 使用

```javascript
// 移除原有的 CompareModal 相关代码：
1. 移除 CompareModal 组件的导入
2. 移除 showCompareModal 状态
3. 移除 compareFolderData 状态
4. 移除 handleCompareClicked 方法
5. 移除 CompareModal 组件的模板引用
6. 清理相关的样式和逻辑
```

### 10. 样式优化

```javascript
// 优化比对区域的样式：
1. 比对区域采用响应式设计，适配不同屏幕尺寸
2. 图片容器设置合适的高度和宽度
3. 添加图片加载时的骨架屏效果
4. 箭头按钮样式美观，有 hover 效果
5. 选中状态的视觉反馈明显
6. 整体布局协调，符合设计规范

// 调整比对区域的样式：
1. 移除弹窗相关的样式
2. 调整为适合嵌入页面的样式
3. 保持左右分栏的布局
4. 添加合适的间距和边框
5. 确保与页面整体风格一致
6. 添加响应式设计，适配不同屏幕尺寸
```

### 11. 错误处理和边界情况

```javascript
// 添加错误处理和边界情况处理：
1. 图片加载失败时显示占位图
2. 网络请求失败时的错误提示
3. 没有选中文件夹或版本时的提示
4. 版本列表为空时的处理
5. 文件夹列表为空时的处理
6. 添加加载状态指示器
```

### 12. 性能优化

```javascript
// 进行性能优化：
1. 图片懒加载，只在需要时加载
2. 版本切换时避免重复请求数据
3. 合理使用 computed 和 watch
4. 避免不必要的组件重新渲染
5. 优化图片缓存策略
```

### 13. 测试和验证

```javascript
// 测试新的比对功能：
1. 测试文件夹单选功能
2. 测试版本多选功能
3. 测试版本切换功能
4. 测试关联功能
5. 测试边界情况（无选择、图片加载失败等）
6. 确保所有功能正常工作
```

## 文件结构变更

### 需要修改的文件：
1. `src/views/cert/batch/TaskWorkspaceView.vue` - 主组件
2. `src/views/cert/folder/components/UnassignedFolderList.vue` - 文件夹列表
3. `src/views/cert/version/components/VersionBrowser.vue` - 版本浏览器
4. `src/views/cert/components/common/CompareModal.vue` - 重构为 CompareArea

### 新增的文件：
1. `src/views/cert/components/common/CompareArea.vue` - 新的比对区域组件

## 注意事项

1. **保持向后兼容**：确保修改不影响其他功能
2. **状态管理**：合理管理组件间的状态传递
3. **用户体验**：确保操作流程顺畅，提供清晰的视觉反馈
4. **错误处理**：完善错误处理和边界情况
5. **性能考虑**：避免不必要的重复渲染和数据请求
6. **代码规范**：遵循项目的代码规范和命名约定

## 完成标准

- [ ] 左侧文件夹列表支持单选
- [ ] 右侧版本浏览器支持多选
- [ ] 比对区域正确显示选中的图片
- [ ] 版本切换功能正常工作
- [ ] 关联功能在比对区域中可用
- [ ] 所有边界情况得到妥善处理
- [ ] 样式美观，响应式设计正常
- [ ] 性能优化措施到位
- [ ] 测试通过，无回归问题 