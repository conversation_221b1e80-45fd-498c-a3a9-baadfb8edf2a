<template>
  <div class="unassigned-folder-list">
    <div v-loading="loading" class="folder-list">
      <FolderListItem
        v-for="folder in unassignedFolders"
        :key="folder.folderId"
        :folder-name="folder.folderName"
        :thumbnail-url="folder.mainPicPath"
        :show-checkbox="true"
        :is-checked="selectedFolders.includes(folder.folderId)"
        :show-compare-button="true"
        @check-change="handleSelection(folder.folderId)"
        @compare-clicked="openCompareModal(folder)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineExpose } from 'vue'
import { ElMessage } from 'element-plus'
import FolderListItem from './FolderListItem/'
import { getFolderList, type FolderInfoVO } from '@/api/cert/folder'

// 定义Props
interface Props {
  taskId: string
}

const props = defineProps<Props>()

// 定义Emits
const emit = defineEmits<{
  'selection-change': [selection: { folderIds: string[], folders: FolderInfoVO[] }]
  'compare-clicked': [folder: FolderInfoVO]
}>()

// 响应式数据
const loading = ref(false)
const unassignedFolders = ref<FolderInfoVO[]>([])
const selectedFolders = ref<string[]>([])

// 获取未分配的文件夹数据
const loadUnassignedFolders = async () => {
  loading.value = true
  try {
    const response = await getFolderList({
      status: 'unassociated',
      taskId: props.taskId
    })
    if (response.code === 200) {
      unassignedFolders.value = response.rows || []
    } else {
      ElMessage.error(response.msg || '加载未分配文件夹失败')
    }
  } catch (error) {
    console.error('加载未分配文件夹失败:', error)
    ElMessage.error('加载未分配文件夹失败')
  } finally {
    loading.value = false
  }
}

// 处理文件夹选择
const handleSelection = (folderId: string) => {
  const index = selectedFolders.value.indexOf(folderId)
  if (index > -1) {
    selectedFolders.value.splice(index, 1)
  } else {
    selectedFolders.value.push(folderId)
  }

  // 触发选择变化事件
  const selectedFolderObjects = unassignedFolders.value.filter(folder =>
    selectedFolders.value.includes(folder.folderId)
  )

  emit('selection-change', {
    folderIds: selectedFolders.value,
    folders: selectedFolderObjects
  })
}

// 打开比对模态框
const openCompareModal = (folder: FolderInfoVO) => {
  emit('compare-clicked', folder)
}

// 刷新数据方法（供父组件调用）
const refreshData = async () => {
  await loadUnassignedFolders()
  // 清空选择
  selectedFolders.value = []
  emit('selection-change', { folderIds: [], folders: [] })
}

// 暴露方法给父组件
defineExpose({
  refreshData
})

// 生命周期
onMounted(() => {
  if (props.taskId) {
    loadUnassignedFolders()
  }
})
</script>

<style scoped>
.unassigned-folder-list {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.folder-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
</style>
