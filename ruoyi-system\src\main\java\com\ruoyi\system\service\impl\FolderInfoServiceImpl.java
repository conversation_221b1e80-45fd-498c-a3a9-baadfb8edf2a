package com.ruoyi.system.service.impl;

import com.mongodb.client.result.UpdateResult;
import com.ruoyi.system.domain.CertType;
import com.ruoyi.system.domain.Country;
import com.ruoyi.system.domain.mongo.FolderInfo;
import com.ruoyi.system.domain.mongo.ImageRepository;
import com.ruoyi.system.domain.mongo.BatchUploadTask;
import com.ruoyi.system.domain.mongo.CertVersion;
import com.ruoyi.system.domain.dto.request.FolderQueryDTO;
import com.ruoyi.system.domain.dto.request.FolderUpdateDTO;
import com.ruoyi.system.domain.dto.request.ReviewActionDTO;

import com.ruoyi.system.domain.dto.response.FolderInfoVO;
import com.ruoyi.system.domain.dto.response.ImageRepositoryVO;
import com.ruoyi.system.repository.FolderInfoRepository;
import com.ruoyi.system.repository.CertVersionRepository;
import com.ruoyi.system.service.IFolderInfoService;
import com.ruoyi.system.service.IImageRepositoryService;
import com.ruoyi.common.service.MinioService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 文件夹信息Service实现类
 * 职责: 管理样本文件夹的业务逻辑，包括查询、根据任务ID删除，以及与图片服务的交互
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class FolderInfoServiceImpl implements IFolderInfoService {
    
    private static final Logger log = LoggerFactory.getLogger(FolderInfoServiceImpl.class);
    
    @Autowired
    private FolderInfoRepository folderInfoRepository;

    @Autowired
    private IImageRepositoryService imageRepositoryService;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private MinioService minioService;

    @Autowired
    private CertVersionRepository certVersionRepository;


    @Override
    public FolderInfo createFolder(FolderInfo folderInfo) {
        try {
            folderInfo.setCreateTime(new Date());
            folderInfo.setUpdateTime(new Date());
            return folderInfoRepository.save(folderInfo);
        } catch (Exception e) {
            log.error("创建文件夹信息失败: {}", e.getMessage(), e);
            throw new RuntimeException("创建文件夹失败", e);
        }
    }
    
    @Override
    public FolderInfoVO getFolderById(String folderId) {
        try {
            FolderInfo folderInfo = folderInfoRepository.findByFolderId(folderId);
            return convertToVO(folderInfo);
        } catch (Exception e) {
            log.error("根据文件夹ID获取文件夹失败: {}", e.getMessage(), e);
            return null;
        }
    }
    
    @Override
    public FolderInfo getFolderByFolderId(String folderId) {
        try {
            return folderInfoRepository.findByFolderId(folderId);
        } catch (Exception e) {
            log.error("根据业务文件夹ID获取文件夹失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public FolderInfo getFolderByMongoId(String id) {
        try {
            return folderInfoRepository.findById(id).orElse(null);
        } catch (Exception e) {
            log.error("根据MongoDB主键获取文件夹失败: {}", e.getMessage(), e);
            return null;
        }
    }
    
    @Override
    public List<FolderInfoVO> getFoldersByTaskId(String taskId) {
        try {
            List<FolderInfo> folders = folderInfoRepository.findByTaskId(taskId);
            return convertToVOList(folders);
        } catch (Exception e) {
            log.error("根据任务ID获取文件夹列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<FolderInfoVO> getFoldersByVersionId(String versionId) {
        try {
            List<FolderInfo> folders = folderInfoRepository.findByVersionId(versionId);
            return convertToVOList(folders);
        } catch (Exception e) {
            log.error("根据版本ID获取文件夹列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<FolderInfoVO> getFoldersByType(String folderType) {
        try {
            List<FolderInfo> folders = folderInfoRepository.findByFolderType(folderType);
            return convertToVOList(folders);
        } catch (Exception e) {
            log.error("根据文件夹类型获取文件夹列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<FolderInfoVO> getFoldersByCountryId(Long countryId) {
        try {
            List<FolderInfo> folders = folderInfoRepository.findByCountryId(countryId);
            return convertToVOList(folders);
        } catch (Exception e) {
            log.error("根据国家ID获取文件夹列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<FolderInfoVO> getFoldersByCertTypeId(Long certTypeId) {
        try {
            List<FolderInfo> folders = folderInfoRepository.findByCertTypeId(certTypeId);
            return convertToVOList(folders);
        } catch (Exception e) {
            log.error("根据证件类型ID获取文件夹列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<FolderInfoVO> getFoldersByIssueYear(String issueYear) {
        try {
            List<FolderInfo> folders = folderInfoRepository.findByIssueYear(issueYear);
            return convertToVOList(folders);
        } catch (Exception e) {
            log.error("根据发行年份获取文件夹列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<FolderInfoVO> getFoldersByStatus(String status) {
        try {
            List<FolderInfo> folders = folderInfoRepository.findByStatus(status);
            return convertToVOList(folders);
        } catch (Exception e) {
            log.error("根据状态获取文件夹列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<FolderInfoVO> getFoldersByDeptId(Long deptId) {
        try {
            List<FolderInfo> folders = folderInfoRepository.findByDeptId(deptId);
            return convertToVOList(folders);
        } catch (Exception e) {
            log.error("根据部门ID获取文件夹列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<FolderInfoVO> getFoldersByUploaderId(Long userId) {
        try {
            List<FolderInfo> folders = folderInfoRepository.findByUploaderId(userId);
            return convertToVOList(folders);
        } catch (Exception e) {
            log.error("根据上传者ID获取文件夹列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public FolderInfoVO getFolderByTaskIdAndName(String taskId, String folderName) {
        try {
            List<FolderInfo> folderInfos = folderInfoRepository.findByTaskIdAndFolderName(taskId, folderName);
            if (folderInfos.isEmpty()) {
                return null;
            }
            // 返回第一个匹配的文件夹
            FolderInfo folderInfo = folderInfos.get(0);
            return convertToVO(folderInfo);
        } catch (Exception e) {
            log.error("根据任务ID和文件夹名称获取文件夹失败: {}", e.getMessage(), e);
            return null;
        }
    }
    
    @Override
    public List<FolderInfoVO> getFoldersByCreateTimeRange(Date startTime, Date endTime) {
        try {
            List<FolderInfo> folders = folderInfoRepository.findByCreateTimeBetween(startTime, endTime);
            return convertToVOList(folders);
        } catch (Exception e) {
            log.error("根据创建时间范围获取文件夹列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<FolderInfoVO> getFoldersByComplexCondition(Long countryId, Long certTypeId, String issueYear) {
        try {
            List<FolderInfo> folders = folderInfoRepository.findByCountryIdAndCertTypeIdAndIssueYear(countryId, certTypeId, issueYear);
            return convertToVOList(folders);
        } catch (Exception e) {
            log.error("根据复合条件获取文件夹列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public FolderInfo updateFolder(FolderInfo folderInfo) {
        try {
            folderInfo.setUpdateTime(new Date());
            return folderInfoRepository.save(folderInfo);
        } catch (Exception e) {
            log.error("更新文件夹信息失败: {}", e.getMessage(), e);
            throw new RuntimeException("更新文件夹失败", e);
        }
    }
    
    @Override
    public boolean updateFolderStatus(String folderId, String status) {
        try {
            FolderInfo folderInfo = folderInfoRepository.findByFolderId(folderId);
            if (folderInfo != null) {
                folderInfo.setStatus(status);
                folderInfo.setUpdateTime(new Date());
                folderInfoRepository.save(folderInfo);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("更新文件夹状态失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean deleteFolder(String folderId) {
        try {
            log.info("开始删除文件夹，folderId: {}", folderId);

            // 1. 获取文件夹下所有图片的MinIO路径
            List<String> minioPaths = imageRepositoryService.getMinioPathsByFolderId(folderId);
            log.info("文件夹{}下共有{}个MinIO文件需要删除", folderId, minioPaths.size());

            // 2. 删除MinIO中的文件
            if (!minioPaths.isEmpty()) {
                try {
                    minioService.deleteFiles(minioPaths);
                    log.info("成功删除{}个MinIO文件", minioPaths.size());
                } catch (Exception e) {
                    log.error("删除MinIO文件失败，但继续删除数据库记录: {}", e.getMessage(), e);
                }
            }

            // 3. 删除文件夹下的所有图片记录
            int deletedImageCount = imageRepositoryService.deleteImagesByFolderId(folderId);
            log.info("删除图片记录{}条", deletedImageCount);

            // 4. 删除文件夹记录
            FolderInfo folderInfo = folderInfoRepository.findByFolderId(folderId);
            if (folderInfo != null) {
                folderInfoRepository.delete(folderInfo);
                log.info("文件夹删除成功，folderId: {}", folderId);
                return true;
            }

            log.warn("文件夹记录不存在，folderId: {}", folderId);
            return false;
        } catch (Exception e) {
            log.error("删除文件夹失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public int deleteFoldersByTaskId(String taskId) {
        try {
            log.info("开始根据任务ID删除文件夹，taskId: {}", taskId);

            List<FolderInfo> folders = folderInfoRepository.findByTaskId(taskId);
            log.info("任务{}下共有{}个文件夹需要删除", taskId, folders.size());

            // 1. 收集所有需要删除的MinIO文件路径
            List<String> allMinioPaths = new ArrayList<>();
            for (FolderInfo folder : folders) {
                List<String> folderMinioPaths = imageRepositoryService.getMinioPathsByFolderId(folder.getFolderId());
                allMinioPaths.addAll(folderMinioPaths);
            }

            log.info("任务{}下共有{}个MinIO文件需要删除", taskId, allMinioPaths.size());

            // 2. 批量删除MinIO中的文件
            if (!allMinioPaths.isEmpty()) {
                try {
                    minioService.deleteFiles(allMinioPaths);
                    log.info("成功删除{}个MinIO文件", allMinioPaths.size());
                } catch (Exception e) {
                    log.error("删除MinIO文件失败，但继续删除数据库记录: {}", e.getMessage(), e);
                }
            }

            // 3. 删除所有图片记录
            int deletedImageCount = 0;
            for (FolderInfo folder : folders) {
                deletedImageCount += imageRepositoryService.deleteImagesByFolderId(folder.getFolderId());
            }
            log.info("删除图片记录{}条", deletedImageCount);

            // 4. 删除文件夹记录
            folderInfoRepository.deleteByTaskId(taskId);

            log.info("任务{}的文件夹删除完成，共删除{}个文件夹", taskId, folders.size());
            return folders.size();
        } catch (Exception e) {
            log.error("根据任务ID删除文件夹失败: {}", e.getMessage(), e);
            return 0;
        }
    }
    
    @Override
    public Map<String, Object> getFolderStatistics(String folderId) {
        Map<String, Object> statistics = new HashMap<>();
        try {
            // 获取图片统计信息
            Map<String, Object> imageStats = imageRepositoryService.getImageStatistics(folderId);
            statistics.putAll(imageStats);
            
            // 添加文件夹基本信息
            FolderInfoVO folder = getFolderById(folderId);
            if (folder != null) {
                statistics.put("folderId", folderId);
                statistics.put("folderName", folder.getFolderName());
                statistics.put("status", folder.getStatus());
                statistics.put("createTime", folder.getCreateTime());
            }
        } catch (Exception e) {
            log.error("获取文件夹统计信息失败: {}", e.getMessage(), e);
        }
        return statistics;
    }
    
    @Override
    public List<FolderInfo> batchCreateFolders(List<FolderInfo> folderInfos) {
        try {
            Date now = new Date();
            for (FolderInfo folderInfo : folderInfos) {
                folderInfo.setCreateTime(now);
                folderInfo.setUpdateTime(now);
            }
            return folderInfoRepository.saveAll(folderInfos);
        } catch (Exception e) {
            log.error("批量创建文件夹失败: {}", e.getMessage(), e);
            throw new RuntimeException("批量创建文件夹失败", e);
        }
    }
    
    @Override
    public boolean existsByTaskIdAndName(String taskId, String folderName) {
        try {
            List<FolderInfo> folderInfos = folderInfoRepository.findByTaskIdAndFolderName(taskId, folderName);
            return !folderInfos.isEmpty();
        } catch (Exception e) {
            log.error("检查文件夹是否存在失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public Map<String, Object> getTaskFolderStatistics(String taskId) {
        Map<String, Object> statistics = new HashMap<>();
        try {
            List<FolderInfo> folders = folderInfoRepository.findByTaskId(taskId);
            statistics.put("totalFolders", folders.size());
            
            // 统计各状态的文件夹数量
            Map<String, Long> statusCount = folders.stream()
                    .collect(Collectors.groupingBy(FolderInfo::getStatus, Collectors.counting()));
            statistics.put("statusCount", statusCount);
            
            // 获取任务的图片统计
            Map<String, Object> imageStats = imageRepositoryService.getTaskImageStatistics(taskId);
            statistics.putAll(imageStats);
            
        } catch (Exception e) {
            log.error("获取任务文件夹统计失败: {}", e.getMessage(), e);
        }
        return statistics;
    }
    
    @Override
    public boolean setAsStandardSample(String folderId, String versionId) {
        try {
            FolderInfo folderInfo = folderInfoRepository.findByFolderId(folderId);
            if (folderInfo != null) {
                folderInfo.setVersionId(versionId);
                folderInfo.setFolderType("STANDARD_SAMPLE");
                folderInfo.setUpdateTime(new Date());
                folderInfoRepository.save(folderInfo);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("设置标准样本失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean unsetStandardSample(String folderId) {
        try {
            FolderInfo folderInfo = folderInfoRepository.findByFolderId(folderId);
            if (folderInfo != null) {
                folderInfo.setFolderType("TRAINING_SAMPLE");
                folderInfo.setUpdateTime(new Date());
                folderInfoRepository.save(folderInfo);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("取消标准样本失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 转换为VO对象
     */
    private FolderInfoVO convertToVO(FolderInfo folderInfo) {
        if (folderInfo == null) {
            return null;
        }

        FolderInfoVO vo = new FolderInfoVO();

        // 复制基本属性
        vo.setId(folderInfo.getId());
        vo.setFolderId(folderInfo.getFolderId());
        vo.setTaskId(folderInfo.getTaskId());
        vo.setVersionId(folderInfo.getVersionId());
        vo.setFolderType(folderInfo.getFolderType());
        vo.setFolderName(folderInfo.getFolderName());
        vo.setIssueYear(folderInfo.getIssueYear());
        vo.setFileCount(folderInfo.getFileCount());
        vo.setProcessedFileCount(folderInfo.getProcessedFileCount());
        vo.setStatus(folderInfo.getStatus());
        vo.setReviewStatus(folderInfo.getReviewStatus());
        vo.setVersionAssociationMethod(folderInfo.getVersionAssociationMethod());
        vo.setCreateTime(folderInfo.getCreateTime());
        vo.setUpdateTime(folderInfo.getUpdateTime());

        // 复制嵌套对象（完整的MySQL实体对象）
        vo.setCountryInfo(folderInfo.getCountryInfo());
        vo.setCertInfo(folderInfo.getCertInfo());
        vo.setDeptInfo(folderInfo.getDeptInfo());
        vo.setUploaderInfo(folderInfo.getUploaderInfo());

        // 复制预解析版本信息
        if (folderInfo.getPreParseVersionInfo() != null) {
            FolderInfoVO.PreParseVersionInfo preParseVO = new FolderInfoVO.PreParseVersionInfo();
            FolderInfo.PreParseVersionInfo preParse = folderInfo.getPreParseVersionInfo();
            
            preParseVO.setOriginalFolderName(preParse.getOriginalFolderName());
            preParseVO.setParsedVersionCode(preParse.getParsedVersionCode());
            preParseVO.setCountryName(preParse.getCountryName());
            preParseVO.setCountryCode(preParse.getCountryCode());
            preParseVO.setCertTypeName(preParse.getCertTypeName());
            preParseVO.setCertTypeCode(preParse.getCertTypeCode());
            preParseVO.setIssueYear(preParse.getIssueYear());
            preParseVO.setCertNumberPrefix(preParse.getCertNumberPrefix());
            preParseVO.setIssuePlace(preParse.getIssuePlace());
            preParseVO.setParseStatus(preParse.getParseStatus());
            preParseVO.setParseErrors(preParse.getParseErrors());
            preParseVO.setParseTime(preParse.getParseTime());
            
            vo.setPreParseVersionInfo(preParseVO);
            
            log.info("预解析版本信息转换完成: parseStatus={}, parsedVersionCode={}", 
                preParse.getParseStatus(), preParse.getParsedVersionCode());
        } else {
            log.info("文件夹[{}]没有预解析版本信息", folderInfo.getFolderId());
        }

        // 设置版本信息
        if (folderInfo.getVersionId() != null && !folderInfo.getVersionId().isEmpty()) {
            try {
                CertVersion version = certVersionRepository.findByVersionId(folderInfo.getVersionId());
                if (version != null) {
                    FolderInfoVO.VersionInfo versionInfo = new FolderInfoVO.VersionInfo();
                    versionInfo.setVersionId(version.getVersionId());
                    versionInfo.setVersionCode(version.getVersionCode());
                    versionInfo.setStandardFolderId(version.getStandardFolderId());
                    versionInfo.setStatus(version.getStatus());
                    vo.setVersionInfo(versionInfo);
                }
            } catch (Exception e) {
                log.warn("获取文件夹[{}]的版本信息失败: {}", folderInfo.getFolderId(), e.getMessage());
            }
        }

        // 添加详细的调试日志
        log.info("=== 转换FolderInfo到VO详细信息 ===");
        log.info("folderId: {}", folderInfo.getFolderId());
        log.info("countryInfo: {}", folderInfo.getCountryInfo());
        log.info("certInfo: {}", folderInfo.getCertInfo());
        log.info("deptInfo: {}", folderInfo.getDeptInfo());
        log.info("uploaderInfo: {}", folderInfo.getUploaderInfo());

        if (folderInfo.getCountryInfo() != null) {
            log.info("countryInfo详情: id={}, name={}, code={}",
                    folderInfo.getCountryInfo().getId(),
                    folderInfo.getCountryInfo().getName(),
                    folderInfo.getCountryInfo().getCode());
        }

        if (folderInfo.getCertInfo() != null) {
            log.info("certInfo详情: id={}, zjlbmc={}, zjlbdm={}",
                    folderInfo.getCertInfo().getId(),
                    folderInfo.getCertInfo().getZjlbmc(),
                    folderInfo.getCertInfo().getZjlbdm());
        }

        if (folderInfo.getDeptInfo() != null) {
            log.info("deptInfo详情: deptId={}, deptName={}",
                    folderInfo.getDeptInfo().getDeptId(),
                    folderInfo.getDeptInfo().getDeptName());
        }

        if (folderInfo.getUploaderInfo() != null) {
            log.info("uploaderInfo详情: userId={}, userName={}",
                    folderInfo.getUploaderInfo().getUserId(),
                    folderInfo.getUploaderInfo().getUserName());
        }

        log.info("=== VO转换后的信息 ===");
        log.info("VO countryInfo: {}", vo.getCountryInfo());
        log.info("VO certInfo: {}", vo.getCertInfo());
        log.info("VO deptInfo: {}", vo.getDeptInfo());
        log.info("VO uploaderInfo: {}", vo.getUploaderInfo());

        return vo;
    }
    
    /**
     * 转换为VO列表
     */
    private List<FolderInfoVO> convertToVOList(List<FolderInfo> folderInfos) {
        return folderInfos.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public boolean associateFolderToVersion(String folderId, String versionId) {
        try {
            log.info("开始关联文件夹[{}]到版本[{}]", folderId, versionId);

            // 1. 验证文件夹是否存在
            FolderInfo folder = folderInfoRepository.findByFolderId(folderId);
            if (folder == null) {
                log.error("文件夹[{}]不存在", folderId);
                throw new RuntimeException("文件夹不存在");
            }

            // 2. 验证文件夹是否已经关联到其他版本
            if (folder.getVersionId() != null && !folder.getVersionId().isEmpty()) {
                log.error("文件夹[{}]已经关联到版本[{}]", folderId, folder.getVersionId());
                throw new RuntimeException("文件夹已经关联到其他版本");
            }

            // 3. 跳过版本验证 - 调用方保证版本存在，避免循环依赖
            log.debug("跳过版本[{}]存在性验证，由调用方保证", versionId);

            // 4. 更新文件夹信息
            folder.setVersionId(versionId);
            folder.setStatus("associated"); // 设置状态为已关联
            folder.setReviewStatus("pending"); // 设置审核状态为待审核
            folder.setVersionAssociationMethod("manual_select"); // 设置版本关联方式为手工选择
            folder.setUpdateTime(new Date());
            folderInfoRepository.save(folder);

            // 5. 批量更新该文件夹下的所有图片的versionId
            Query imageQuery = new Query(Criteria.where("folderId").is(folderId));
            Update imageUpdate = new Update()
                    .set("versionId", versionId)
                    .set("updateTime", new Date());

            long updatedImageCount = mongoTemplate.updateMulti(imageQuery, imageUpdate, "image_repository").getModifiedCount();
            log.info("成功更新[{}]张图片的版本ID", updatedImageCount);

            log.info("成功关联文件夹[{}]到版本[{}]", folderId, versionId);
            return true;

        } catch (Exception e) {
            log.error("关联文件夹[{}]到版本[{}]失败: {}", folderId, versionId, e.getMessage(), e);
            throw new RuntimeException("关联文件夹到版本失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<FolderInfoVO> getFoldersByKeyword(String keyword) {
        try {
            List<FolderInfo> folders = folderInfoRepository.findByFolderNameContainingIgnoreCase(keyword);
            return convertToVOList(folders);
        } catch (Exception e) {
            log.error("根据关键词获取文件夹列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<FolderInfoVO> getAllFolders() {
        try {
            List<FolderInfo> folders = folderInfoRepository.findAll();
            log.info("从数据库获取到{}个文件夹记录", folders.size());

            // 添加详细的调试日志
            if (!folders.isEmpty()) {
                FolderInfo firstFolder = folders.get(0);
                log.info("第一个文件夹详情: folderId={}, countryInfo={}, certInfo={}, deptInfo={}, uploaderInfo={}",
                        firstFolder.getFolderId(),
                        firstFolder.getCountryInfo(),
                        firstFolder.getCertInfo(),
                        firstFolder.getDeptInfo(),
                        firstFolder.getUploaderInfo());
            }

            return convertToVOList(folders);
        } catch (Exception e) {
            log.error("获取所有文件夹列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<FolderInfoVO> getFoldersByCountryCode(String countryCode) {
        try {
            List<FolderInfo> folders = folderInfoRepository.findByCountryCode(countryCode);
            return convertToVOList(folders);
        } catch (Exception e) {
            log.error("根据国家代码获取文件夹列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<FolderInfoVO> getFoldersByCertTypeCode(String certTypeCode) {
        try {
            List<FolderInfo> folders = folderInfoRepository.findByCertTypeCode(certTypeCode);
            return convertToVOList(folders);
        } catch (Exception e) {
            log.error("根据证件类型代码获取文件夹列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional
    public boolean updateFolderInfo(String folderId, FolderUpdateDTO updateDTO) {
        try {
            log.info("开始更新文件夹信息: folderId={}", folderId);

            // 1. 验证文件夹是否存在
            FolderInfo folder = folderInfoRepository.findByFolderId(folderId);
            if (folder == null) {
                log.error("文件夹不存在: {}", folderId);
                throw new RuntimeException("文件夹不存在");
            }

            // 2. 构建国家信息对象
            Country countryInfo = null;
            if (updateDTO.getCountryInfo() != null) {
                countryInfo = new Country();
                countryInfo.setId(updateDTO.getCountryInfo().getId());
                countryInfo.setName(updateDTO.getCountryInfo().getName());
                countryInfo.setNameEn(updateDTO.getCountryInfo().getNameEn());
                countryInfo.setCode(updateDTO.getCountryInfo().getCode());
            }

            // 3. 构建证件类型信息对象
            CertType certInfo = null;
            if (updateDTO.getCertInfo() != null) {
                certInfo = new CertType();
                certInfo.setId(updateDTO.getCertInfo().getId());
                certInfo.setZjlbdm(updateDTO.getCertInfo().getZjlbdm());
                certInfo.setZjlbmc(updateDTO.getCertInfo().getZjlbmc());
            }

            // 4. 更新文件夹基本信息
            folder.setFolderName(updateDTO.getFolderName());
            folder.setFolderType(updateDTO.getFolderType());
            folder.setStatus(updateDTO.getStatus());
            folder.setReviewStatus(updateDTO.getReviewStatus());
            folder.setVersionAssociationMethod(updateDTO.getVersionAssociationMethod());
            folder.setIssueYear(updateDTO.getIssueYear());
            folder.setVersionId(updateDTO.getVersionId());
            folder.setFileCount(updateDTO.getFileCount());
            folder.setProcessedFileCount(updateDTO.getProcessedFileCount());
            folder.setCountryInfo(countryInfo);
            folder.setCertInfo(certInfo);
            folder.setUpdateTime(new Date());

            // 5. 更新审核信息
            if (updateDTO.getReviewComments() != null && !updateDTO.getReviewComments().trim().isEmpty()) {
                FolderInfo.ReviewInfo reviewInfo = folder.getReviewInfo();
                if (reviewInfo == null) {
                    reviewInfo = new FolderInfo.ReviewInfo();
                    folder.setReviewInfo(reviewInfo);
                }
                reviewInfo.setReviewComments(updateDTO.getReviewComments());
                reviewInfo.setReviewTime(new Date());
            }

            // 6. 保存更新
            FolderInfo savedFolder = folderInfoRepository.save(folder);

            if ("standard".equals(updateDTO.getFolderType())) {
                log.info("已将文件夹[{}]设置为标准样本文件夹", folderId);
            } else if ("regular".equals(updateDTO.getFolderType())) {
                log.info("已将文件夹[{}]设置为普通文件夹", folderId);
            }

            log.info("成功更新文件夹信息: folderId={}", folderId);
            return savedFolder != null;

        } catch (Exception e) {
            log.error("更新文件夹信息失败: folderId={}, 错误: {}", folderId, e.getMessage(), e);
            throw new RuntimeException("更新文件夹信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新文件夹下所有图片的样本类型和标注权限
     * 注意：此方法已废弃，因为sampleType已移至FolderInfo，isAnnotatable已移除
     * 现在标注权限基于folderType和isAnnotatableType判断
     *
     * @param folderId 文件夹ID
     * @param sampleType 样本类型 (已废弃)
     * @param isAnnotatable 是否可标注 (已废弃)
     */
    @Deprecated
    private void updateImagesSampleType(String folderId, String sampleType, boolean isAnnotatable) {
        try {
            // 此方法已废弃，不再更新图片级别的sampleType和isAnnotatable字段
            // 标注权限现在基于文件夹的folderType和图片的isAnnotatableType判断
            log.info("updateImagesSampleType方法已废弃，folderId={}", folderId);

        } catch (Exception e) {
            log.error("更新图片样本类型失败: {}", e.getMessage(), e);
            throw new RuntimeException("更新图片样本类型失败: " + e.getMessage(), e);
        }
    }

    // ==================== 版本审核和样本分类相关方法实现 ====================

    @Override
    public FolderInfo.ThirdPartyDetectionResult detectVersionByThirdParty(String folderId) {
        try {
            log.info("开始对文件夹[{}]进行第三方版本检测", folderId);

            // 1. 验证文件夹是否存在
            FolderInfo folder = folderInfoRepository.findByFolderId(folderId);
            if (folder == null) {
                log.error("文件夹[{}]不存在", folderId);
                throw new RuntimeException("文件夹不存在");
            }

            // 2. 获取文件夹下的图片列表
            List<ImageRepository> images = mongoTemplate.find(
                    new Query(Criteria.where("folderId").is(folderId)),
                    ImageRepository.class,
                    "image_repository"
            );

            if (images.isEmpty()) {
                log.warn("文件夹[{}]下没有图片，无法进行检测", folderId);
                throw new RuntimeException("文件夹下没有图片");
            }

            // 3. 创建检测结果对象
            FolderInfo.ThirdPartyDetectionResult result = new FolderInfo.ThirdPartyDetectionResult();
            result.setDetectionId("DETECT_" + System.currentTimeMillis());
            result.setDetectionTime(new Date());

            // 4. 模拟第三方API调用（实际项目中应该调用真实的第三方服务）
            try {
                // TODO: 这里应该调用实际的第三方检测API
                // String apiResponse = thirdPartyApiService.detectVersion(images);

                // 模拟检测结果
                List<FolderInfo.VersionMatch> matchedVersions = new ArrayList<>();
                FolderInfo.VersionMatch match = new FolderInfo.VersionMatch();
                match.setVersionId("VERSION_" + System.currentTimeMillis());
                match.setVersionCode("AUTO_DETECTED_V1");
                match.setMatchScore(0.85);
                match.setMatchReason("图片特征匹配度较高");
                matchedVersions.add(match);

                result.setDetectionStatus("success");
                result.setConfidence(0.85);
                result.setMatchedVersions(matchedVersions);

                log.info("第三方检测成功，文件夹[{}]检测到{}个匹配版本", folderId, matchedVersions.size());

            } catch (Exception apiException) {
                log.error("第三方API调用失败: {}", apiException.getMessage(), apiException);
                result.setDetectionStatus("failed");
                result.setErrorMessage("第三方API调用失败: " + apiException.getMessage());
            }

            // 5. 保存检测结果到文件夹
            folder.setThirdPartyDetectionResult(result);
            folder.setUpdateTime(new Date());
            folderInfoRepository.save(folder);

            log.info("第三方检测完成，文件夹[{}]检测状态: {}", folderId, result.getDetectionStatus());
            return result;

        } catch (Exception e) {
            log.error("第三方版本检测失败: {}", e.getMessage(), e);
            throw new RuntimeException("第三方版本检测失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<FolderInfo> getPendingReviewFolders(FolderQueryDTO queryDTO) {
        try {
            log.info("查询待审核文件夹列表，查询条件: {}", queryDTO);

            // 构建查询条件
            Query query = new Query();

            // 基础条件：审核状态为待审核
            query.addCriteria(Criteria.where("reviewStatus").is("pending"));

            // 添加其他查询条件
            if (queryDTO.getTaskId() != null && !queryDTO.getTaskId().isEmpty()) {
                query.addCriteria(Criteria.where("taskId").is(queryDTO.getTaskId()));
            }

            if (queryDTO.getVersionId() != null && !queryDTO.getVersionId().isEmpty()) {
                query.addCriteria(Criteria.where("versionId").is(queryDTO.getVersionId()));
            }

            if (queryDTO.getFolderType() != null && !queryDTO.getFolderType().isEmpty()) {
                query.addCriteria(Criteria.where("folderType").is(queryDTO.getFolderType()));
            }

            if (queryDTO.getCountryId() != null) {
                query.addCriteria(Criteria.where("countryInfo.id").is(queryDTO.getCountryId()));
            }

            if (queryDTO.getCertTypeId() != null) {
                query.addCriteria(Criteria.where("certInfo.id").is(queryDTO.getCertTypeId()));
            }

            if (queryDTO.getIssueYear() != null && !queryDTO.getIssueYear().isEmpty()) {
                query.addCriteria(Criteria.where("issueYear").is(queryDTO.getIssueYear()));
            }

            // 执行查询
            List<FolderInfo> folders = mongoTemplate.find(query, FolderInfo.class, "folder_info");

            log.info("查询到{}个待审核文件夹", folders.size());
            return folders;

        } catch (Exception e) {
            log.error("查询待审核文件夹列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional
    public boolean reviewFolderVersion(String folderId, ReviewActionDTO dto) {
        try {
            log.info("开始审核文件夹[{}]版本关联，操作: {}", folderId, dto.getAction());

            // 1. 验证文件夹是否存在
            FolderInfo folder = folderInfoRepository.findByFolderId(folderId);
            if (folder == null) {
                log.error("文件夹[{}]不存在", folderId);
                throw new RuntimeException("文件夹不存在");
            }

            // 2. 验证审核操作
            if (!"approve".equals(dto.getAction()) && !"reject".equals(dto.getAction())) {
                log.error("无效的审核操作: {}", dto.getAction());
                throw new RuntimeException("无效的审核操作，只支持 approve 或 reject");
            }

            // 3. 更新审核状态
            folder.setReviewStatus(dto.getAction().equals("approve") ? "approved" : "rejected");

            // 4. 记录审核信息
            FolderInfo.ReviewInfo reviewInfo = folder.getReviewInfo();
            if (reviewInfo == null) {
                reviewInfo = new FolderInfo.ReviewInfo();
                folder.setReviewInfo(reviewInfo);
            }

            reviewInfo.setReviewerId(dto.getReviewerId());
            reviewInfo.setReviewerName(dto.getReviewerName());
            reviewInfo.setReviewTime(new Date());
            reviewInfo.setReviewComments(dto.getComments());

            // 5. 添加审核历史记录
            List<FolderInfo.ReviewRecord> reviewHistory = reviewInfo.getReviewHistory();
            if (reviewHistory == null) {
                reviewHistory = new ArrayList<>();
                reviewInfo.setReviewHistory(reviewHistory);
            }

            FolderInfo.ReviewRecord record = new FolderInfo.ReviewRecord();
            record.setAction(dto.getAction());
            record.setReviewerId(dto.getReviewerId());
            record.setActionTime(new Date());
            record.setComments(dto.getComments());
            reviewHistory.add(record);

            // 6. 如果审核驳回，清除版本关联
            if ("reject".equals(dto.getAction())) {
                folder.setVersionId(null);
                folder.setStatus("unassociated");

                // 同时清除该文件夹下所有图片的版本关联
                Query imageQuery = new Query(Criteria.where("folderId").is(folderId));
                Update imageUpdate = new Update()
                        .unset("versionId")
                        .set("updateTime", new Date());

                long updatedImageCount = mongoTemplate.updateMulti(imageQuery, imageUpdate, "image_repository").getModifiedCount();
                log.info("审核驳回，清除了[{}]张图片的版本关联", updatedImageCount);
            }

            // 7. 保存文件夹更新
            folder.setUpdateTime(new Date());
            folderInfoRepository.save(folder);

            log.info("文件夹[{}]审核完成，结果: {}", folderId, dto.getAction());
            return true;

        } catch (Exception e) {
            log.error("审核文件夹[{}]版本关联失败: {}", folderId, e.getMessage(), e);
            throw new RuntimeException("审核文件夹版本关联失败: " + e.getMessage(), e);
        }
    }



    @Override
    @Transactional
    public boolean fixFolderStatus(String folderId) {
        try {
            log.info("开始修复文件夹状态，folderId: {}", folderId);

            // 查询文件夹信息
            FolderInfo folder = folderInfoRepository.findByFolderId(folderId);
            if (folder == null) {
                log.warn("文件夹不存在，folderId: {}", folderId);
                return false;
            }

            // 检查是否需要修复
            if (!"PROCESSING".equals(folder.getStatus())) {
                log.info("文件夹状态不是PROCESSING，无需修复，当前状态: {}", folder.getStatus());
                return false;
            }

            // 检查是否已完成上传
            if (folder.getProcessedFileCount() < folder.getFileCount()) {
                log.warn("文件夹上传未完成，无法修复状态。已上传: {}, 总数: {}",
                        folder.getProcessedFileCount(), folder.getFileCount());
                return false;
            }

            // 修复状态
            Query query = new Query(Criteria.where("folderId").is(folderId));
            Update update = new Update()
                    .set("status", "unassociated")
                    .set("updateTime", new Date());

            mongoTemplate.updateFirst(query, update, FolderInfo.class);

            log.info("文件夹状态修复成功，folderId: {}, PROCESSING → unassociated", folderId);
            return true;

        } catch (Exception e) {
            log.error("修复文件夹状态失败，folderId: {}", folderId, e);
            throw new RuntimeException("修复文件夹状态失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> prepareResumeUpload(String folderId) {
        try {
            log.info("准备继续上传，folderId: {}", folderId);

            // 查询文件夹信息
            FolderInfo folder = folderInfoRepository.findByFolderId(folderId);
            if (folder == null) {
                throw new RuntimeException("文件夹不存在");
            }

            // 检查状态
            if (!"PROCESSING".equals(folder.getStatus())) {
                throw new RuntimeException("文件夹状态不是PROCESSING，无法继续上传");
            }

            // 查询已上传的文件
            List<ImageRepositoryVO> uploadedImages = imageRepositoryService.getImagesByFolderId(folderId);
            List<String> uploadedFileNames = uploadedImages.stream()
                    .map(ImageRepositoryVO::getOriginalFileName)
                    .collect(Collectors.toList());

            // 准备返回信息
            Map<String, Object> result = new HashMap<>();
            result.put("taskId", folder.getTaskId());
            result.put("folderId", folderId);
            result.put("folderName", folder.getFolderName());
            result.put("totalFiles", folder.getFileCount());
            result.put("uploadedFiles", folder.getProcessedFileCount());
            result.put("uploadedFileNames", uploadedFileNames);
            result.put("remainingFiles", folder.getFileCount() - folder.getProcessedFileCount());

            // 查询BatchUploadTask获取更多信息
            BatchUploadTask task = null;
            try {
                // 这里需要注入BatchUploadTaskRepository或通过其他方式获取
                // 暂时使用空值，后续可以完善
                log.debug("需要获取BatchUploadTask信息，taskId: {}", folder.getTaskId());
            } catch (Exception e) {
                log.warn("获取BatchUploadTask信息失败: {}", e.getMessage());
            }

            // 上传配置信息
            Map<String, Object> uploadConfig = new HashMap<>();
            uploadConfig.put("endpoint", "/dev-api/tus/uploads");
            uploadConfig.put("chunkSize", 5 * 1024 * 1024); // 5MB

            // 创建metadata Map
            Map<String, String> metadata = new HashMap<>();
            metadata.put("taskId", folder.getTaskId());
            metadata.put("certNumberPrefix", ""); // 暂时为空，需要从BatchUploadTask获取
            metadata.put("issuePlace", ""); // 暂时为空，需要从BatchUploadTask获取
            uploadConfig.put("metadata", metadata);

            result.put("uploadConfig", uploadConfig);

            log.info("继续上传准备完成，folderId: {}, 剩余文件: {}",
                    folderId, folder.getFileCount() - folder.getProcessedFileCount());

            return result;

        } catch (Exception e) {
            log.error("准备继续上传失败，folderId: {}", folderId, e);
            throw new RuntimeException("准备继续上传失败: " + e.getMessage(), e);
        }
    }

    // ==================== 标准样本管理方法实现 ====================

    @Override
    @Transactional
    public boolean setStandardFolder(String versionId, String folderId) {
        try {
            log.info("设置标准样本文件夹: versionId={}, folderId={}", versionId, folderId);

            // 1. 验证文件夹是否存在且属于指定版本
            FolderInfo folder = folderInfoRepository.findByFolderId(folderId);
            if (folder == null) {
                log.error("文件夹不存在: {}", folderId);
                throw new RuntimeException("文件夹不存在");
            }

            if (!versionId.equals(folder.getVersionId())) {
                log.error("文件夹不属于指定版本: folderId={}, expectedVersionId={}, actualVersionId={}",
                        folderId, versionId, folder.getVersionId());
                throw new RuntimeException("文件夹不属于指定版本");
            }

            // 2. 检查版本是否已有标准样本文件夹
            Optional<FolderInfo> existingStandard = folderInfoRepository.findByVersionIdAndFolderType(versionId, "standard");
            if (existingStandard.isPresent() && !folderId.equals(existingStandard.get().getFolderId())) {
                log.error("版本已有标准样本文件夹: versionId={}, existingFolderId={}", versionId, existingStandard.get().getFolderId());
                throw new RuntimeException("版本已有标准样本文件夹，请先取消现有标准样本");
            }

            // 3. 设置为标准样本
            folder.setFolderType("standard");
            folder.setUpdateTime(new Date());
            folderInfoRepository.save(folder);

            // 4. 标注权限现在基于folderType自动判断，无需更新图片字段

            log.info("设置标准样本文件夹成功: versionId={}, folderId={}", versionId, folderId);
            return true;

        } catch (Exception e) {
            log.error("设置标准样本文件夹失败: versionId={}, folderId={}, error={}", versionId, folderId, e.getMessage(), e);
            throw new RuntimeException("设置标准样本文件夹失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public boolean replaceStandardFolder(String versionId, String newFolderId, boolean keepAnnotations) {
        try {
            log.info("替换标准样本文件夹: versionId={}, newFolderId={}, keepAnnotations={}",
                    versionId, newFolderId, keepAnnotations);

            // 1. 获取当前标准样本文件夹
            Optional<FolderInfo> currentStandard = folderInfoRepository.findByVersionIdAndFolderType(versionId, "standard");
            if (!currentStandard.isPresent()) {
                log.warn("版本没有标准样本文件夹，直接设置新的: versionId={}", versionId);
                return setStandardFolder(versionId, newFolderId);
            }

            String oldFolderId = currentStandard.get().getFolderId();

            // 2. 验证新文件夹
            FolderInfo newFolder = folderInfoRepository.findByFolderId(newFolderId);
            if (newFolder == null || !versionId.equals(newFolder.getVersionId())) {
                throw new RuntimeException("新文件夹无效或不属于指定版本");
            }

            // 3. 如果不保留标注，删除现有模板
            if (!keepAnnotations) {
                // 这里需要注入VersionAnnotationTemplateService，暂时跳过
                log.info("不保留标注，将删除现有模板");
            }

            // 4. 取消旧标准样本
            FolderInfo oldFolder = currentStandard.get();
            oldFolder.setFolderType("regular");
            oldFolder.setUpdateTime(new Date());
            folderInfoRepository.save(oldFolder);

            // 5. 设置新标准样本
            newFolder.setFolderType("standard");
            newFolder.setUpdateTime(new Date());
            folderInfoRepository.save(newFolder);

            // 6. 更新图片的可标注状态
            updateFolderImagesAnnotatableStatus(oldFolderId, false);
            updateFolderImagesAnnotatableStatus(newFolderId, true);

            log.info("替换标准样本文件夹成功: versionId={}, oldFolderId={}, newFolderId={}",
                    versionId, oldFolderId, newFolderId);
            return true;

        } catch (Exception e) {
            log.error("替换标准样本文件夹失败: versionId={}, newFolderId={}, error={}",
                    versionId, newFolderId, e.getMessage(), e);
            throw new RuntimeException("替换标准样本文件夹失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public boolean removeStandardFolder(String versionId, String folderId) {
        try {
            log.info("取消标准样本文件夹: versionId={}, folderId={}", versionId, folderId);

            // 1. 验证文件夹是否为标准样本
            FolderInfo folder = folderInfoRepository.findByFolderId(folderId);
            if (folder == null) {
                throw new RuntimeException("文件夹不存在");
            }

            if (!"standard".equals(folder.getFolderType())) {
                log.warn("文件夹不是标准样本: folderId={}", folderId);
                return true; // 已经不是标准样本，返回成功
            }

            if (!versionId.equals(folder.getVersionId())) {
                throw new RuntimeException("文件夹不属于指定版本");
            }

            // 2. 取消标准样本标志
            folder.setFolderType("regular");
            folder.setUpdateTime(new Date());
            folderInfoRepository.save(folder);

            // 3. 更新文件夹下图片的可标注状态
            updateFolderImagesAnnotatableStatus(folderId, false);

            // 4. 删除相关的标注模板（这里需要注入VersionAnnotationTemplateService）
            log.info("需要删除标准文件夹的标注模板: folderId={}", folderId);

            log.info("取消标准样本文件夹成功: versionId={}, folderId={}", versionId, folderId);
            return true;

        } catch (Exception e) {
            log.error("取消标准样本文件夹失败: versionId={}, folderId={}, error={}",
                    versionId, folderId, e.getMessage(), e);
            throw new RuntimeException("取消标准样本文件夹失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean hasStandardFolder(String versionId) {
        try {
            return folderInfoRepository.existsByVersionIdAndFolderType(versionId, "standard");
        } catch (Exception e) {
            log.error("检查版本是否有标准样本文件夹失败: versionId={}, error={}", versionId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Optional<FolderInfo> getStandardFolder(String versionId) {
        try {
            return folderInfoRepository.findByVersionIdAndFolderType(versionId, "standard");
        } catch (Exception e) {
            log.error("获取版本标准样本文件夹失败: versionId={}, error={}", versionId, e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Override
    public StandardSampleInfo getStandardSampleInfo(String folderId) {
        try {
            FolderInfo folder = folderInfoRepository.findByFolderId(folderId);
            if (folder == null) {
                return new StandardSampleInfo(false, "文件夹不存在");
            }

            StandardSampleInfo info = new StandardSampleInfo();
            boolean isStandardSample = "standard".equals(folder.getFolderType());
            info.setStandardSample(isStandardSample);
            info.setVersionId(folder.getVersionId());

            if (isStandardSample) {
                info.setCanRemoveStandard(true);
                info.setCanSetAsStandard(false);
                info.setReason("当前为标准样本文件夹");
                // 这里可以统计模板数量，需要注入VersionAnnotationTemplateService
                info.setTemplateCount(0);
            } else {
                // 检查版本是否已有标准样本
                boolean hasStandard = hasStandardFolder(folder.getVersionId());
                info.setCanSetAsStandard(!hasStandard);
                info.setCanRemoveStandard(false);

                if (hasStandard) {
                    info.setReason("版本已有标准样本文件夹");
                } else {
                    info.setReason("可以设置为标准样本");
                }
            }

            return info;

        } catch (Exception e) {
            log.error("获取文件夹标准样本信息失败: folderId={}, error={}", folderId, e.getMessage(), e);
            return new StandardSampleInfo(false, "获取信息失败: " + e.getMessage());
        }
    }

    /**
     * 更新文件夹下图片的可标注状态
     * 注意：此方法已废弃，因为isAnnotatable字段已移除
     * 现在标注权限基于folderType和isAnnotatableType判断
     */
    @Deprecated
    private void updateFolderImagesAnnotatableStatus(String folderId, boolean isAnnotatable) {
        try {
            // 此方法已废弃，不再更新图片级别的isAnnotatable字段
            // 标注权限现在基于文件夹的folderType和图片的isAnnotatableType判断
            log.info("updateFolderImagesAnnotatableStatus方法已废弃，folderId={}", folderId);

        } catch (Exception e) {
            log.error("更新文件夹图片可标注状态失败: folderId={}, error={}", folderId, e.getMessage(), e);
        }
    }

    // ==================== 便捷工具方法 ====================

    @Override
    public boolean isStandardSampleFolder(String folderId) {
        try {
            FolderInfo folder = folderInfoRepository.findByFolderId(folderId);
            return folder != null && "standard".equals(folder.getFolderType());
        } catch (Exception e) {
            log.error("判断标准样本文件夹失败: folderId={}, error={}", folderId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean canAnnotateImage(String imageId) {
        try {
            ImageRepository image = mongoTemplate.findOne(
                    new Query(Criteria.where("imageId").is(imageId)),
                    ImageRepository.class,
                    "image_repository"
            );
            if (image == null || !Boolean.TRUE.equals(image.getIsAnnotatableType())) {
                return false;
            }
            return isStandardSampleFolder(image.getFolderId());
        } catch (Exception e) {
            log.error("判断图片可标注性失败: imageId={}, error={}", imageId, e.getMessage(), e);
            return false;
        }
    }
}
