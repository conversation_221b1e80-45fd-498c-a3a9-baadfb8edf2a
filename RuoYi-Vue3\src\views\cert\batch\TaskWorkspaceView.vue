<template>
  <div class="task-workspace">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="page-title">
        <h2>任务工作台</h2>
        <p>管理任务 {{ taskId }} 的文件夹资源与版本成果</p>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <el-container class="main-container">
      <!-- 左侧栏：待处理文件夹 -->
      <el-aside width="400px" class="left-panel">
        <div class="panel-header">
          <h3>待处理文件夹</h3>
          <el-tag type="info">{{ selectedFolderIds.length }} 个已选择</el-tag>
        </div>

        <UnassignedFolderList
          :task-id="taskId"
          @selection-change="handleFolderSelectionChange"
          @compare-clicked="handleCompareClicked"
          ref="unassignedFolderListRef"
        />
      </el-aside>

      <!-- 右侧栏：版本列表 -->
      <el-main class="right-panel">
        <div class="panel-header">
          <h3>版本成果</h3>
        </div>

        <VersionBrowser
          :context-keywords="contextKeywords"
          @create-version-clicked="handleCreateVersionClicked"
          ref="versionBrowserRef"
        />
      </el-main>
    </el-container>

    <!-- 比对模态框 -->
    <CompareModal
      v-model="showCompareModal"
      :folder-data="compareFolderData"
    />

    <!-- 创建版本确认弹窗 -->
    <el-dialog
      v-model="showCreateVersionDialog"
      title="创建新版本"
      width="500px"
      :close-on-click-escape="false"
      :close-on-press-escape="false"
    >
      <div class="create-version-content">
        <div class="selected-folders-info">
          <h4>已选择的文件夹 ({{ selectedFolderIds.length }} 个)</h4>
          <div class="folder-list">
            <el-tag
              v-for="folderId in selectedFolderIds"
              :key="folderId"
              type="info"
              size="small"
              class="folder-tag"
            >
              {{ getFolderNameById(folderId) }}
            </el-tag>
          </div>
        </div>

        <el-form :model="createVersionForm" :rules="createVersionRules" ref="createVersionFormRef">
          <el-form-item label="版本名称" prop="versionName">
            <el-input
              v-model="createVersionForm.versionName"
              placeholder="请输入新版本名称"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="版本描述" prop="description">
            <el-input
              v-model="createVersionForm.description"
              type="textarea"
              placeholder="请输入版本描述（可选）"
              :rows="3"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showCreateVersionDialog = false" :disabled="creatingVersion">
            取消
          </el-button>
          <el-button
            type="primary"
            @click="confirmCreateVersion"
            :loading="creatingVersion"
          >
            创建版本
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import UnassignedFolderList from '../folder/components/UnassignedFolderList.vue'
import VersionBrowser from '../version/components/VersionBrowser.vue'
import CompareModal from '@/views/cert/components/common/CompareModal.vue'
import { createVersionFromFolder } from '@/api/cert/version'
import { associateFolderToVersion } from '@/api/cert/folder'
import type { FolderInfoVO } from '@/api/cert/folder'

// 路由参数
const route = useRoute()
const taskId = computed(() => route.params.taskId as string)

// 子组件引用
const unassignedFolderListRef = ref()
const versionBrowserRef = ref()

// 响应式数据
const selectedFolders = ref<FolderInfoVO[]>([])
const selectedFolderIds = ref<string[]>([])
const showCompareModal = ref(false)
const compareFolderData = ref<FolderInfoVO | null>(null)
const showCreateVersionDialog = ref(false)
const creatingVersion = ref(false)
const createVersionFormRef = ref()

// 创建版本表单
const createVersionForm = reactive({
  versionName: '',
  description: ''
})

// 表单验证规则
const createVersionRules = {
  versionName: [
    { required: true, message: '请输入版本名称', trigger: 'blur' },
    { min: 2, max: 100, message: '版本名称长度应在 2 到 100 个字符之间', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '描述长度不能超过 500 个字符', trigger: 'blur' }
  ]
}

// 计算属性：从选中的文件夹中提取关键词
const contextKeywords = computed(() => {
  const keywords: string[] = []

  selectedFolders.value.forEach(folder => {
    if (folder.folderName) {
      // 从文件夹名称中提取关键词，例如 "西班牙_公务普通护照_2001_XDD85_哈瓦那"
      const parts = folder.folderName.split('_')
      if (parts.length > 0) {
        keywords.push(parts[0]) // 提取国家名
      }
      if (parts.length > 1) {
        keywords.push(parts[1]) // 提取证件类型
      }
    }
  })

  // 去重并返回
  return [...new Set(keywords)]
})

// 文件夹名称缓存（用于显示）
const folderNameCache = ref<Map<string, string>>(new Map())

// 获取文件夹名称
const getFolderNameById = (folderId: string): string => {
  return folderNameCache.value.get(folderId) || folderId
}

// 处理文件夹选择变化
const handleFolderSelectionChange = (selection: { folderIds: string[], folders: FolderInfoVO[] }) => {
  selectedFolderIds.value = selection.folderIds
  selectedFolders.value = selection.folders

  // 更新文件夹名称缓存
  selection.folders.forEach(folder => {
    folderNameCache.value.set(folder.folderId, folder.folderName)
  })
}

// 处理比对按钮点击
const handleCompareClicked = (folder: FolderInfoVO) => {
  compareFolderData.value = folder
  showCompareModal.value = true
}

// 处理创建版本按钮点击
const handleCreateVersionClicked = () => {
  if (selectedFolderIds.value.length === 0) {
    ElMessage.warning('请至少选择一个待处理的文件夹')
    return
  }

  // 重置表单
  createVersionForm.versionName = ''
  createVersionForm.description = ''

  showCreateVersionDialog.value = true
}

// 确认创建版本
const confirmCreateVersion = async () => {
  try {
    // 表单验证
    await createVersionFormRef.value?.validate()

    creatingVersion.value = true

    // 创建版本并关联所有选中的文件夹
    const results = await Promise.allSettled(
      selectedFolderIds.value.map(async (folderId, index) => {
        try {
          // 为每个文件夹创建版本
          const versionCode = `${createVersionForm.versionName}_${index + 1}`
          const description = createVersionForm.description || `基于文件夹创建的版本`

          const createResponse = await createVersionFromFolder({
            folderId,
            versionCode,
            description
          })

          if (createResponse.code === 200) {
            return {
              success: true,
              folderId,
              versionId: createResponse.data.versionId,
              versionCode: createResponse.data.versionCode
            }
          } else {
            throw new Error(createResponse.msg || '创建版本失败')
          }
        } catch (error) {
          return {
            success: false,
            folderId,
            error: error.message || '创建版本失败'
          }
        }
      })
    )

    // 统计结果
    const successResults = results.filter(r => r.status === 'fulfilled' && r.value.success)
    const failedResults = results.filter(r => r.status === 'rejected' || (r.status === 'fulfilled' && !r.value.success))

    // 显示结果
    if (successResults.length > 0) {
      ElMessage.success(`成功创建 ${successResults.length} 个版本`)
    }

    if (failedResults.length > 0) {
      ElMessage.error(`${failedResults.length} 个版本创建失败，请重试`)
    }

    // 关闭弹窗
    showCreateVersionDialog.value = false

    // 清空选择
    selectedFolderIds.value = []

    // 刷新两个子组件的数据
    await refreshComponents()

  } catch (error) {
    console.error('创建版本失败:', error)
    ElMessage.error('创建版本失败: ' + (error.message || '未知错误'))
  } finally {
    creatingVersion.value = false
  }
}

// 刷新子组件数据
const refreshComponents = async () => {
  try {
    // 刷新未分配文件夹列表
    if (unassignedFolderListRef.value?.refreshData) {
      await unassignedFolderListRef.value.refreshData()
    }

    // 刷新版本浏览器
    if (versionBrowserRef.value?.refreshData) {
      await versionBrowserRef.value.refreshData()
    }
  } catch (error) {
    console.error('刷新组件数据失败:', error)
  }
}

// 生命周期
onMounted(() => {
  if (!taskId.value) {
    ElMessage.error('任务ID不能为空')
    return
  }
})
</script>

<style scoped>
.task-workspace {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.page-header {
  padding: 20px 24px 0;
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
}

.page-title h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-title p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.main-container {
  flex: 1;
  height: calc(100vh - 100px);
}

.left-panel {
  background-color: #fff;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  padding: 0;
}

.right-panel {
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fafafa;
}

.panel-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.create-version-content {
  padding: 20px 0;
}

.selected-folders-info {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.selected-folders-info h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.folder-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.folder-tag {
  margin: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-aside) {
  overflow: hidden;
  padding: 0 !important;
}

:deep(.el-main) {
  padding: 0;
  overflow: hidden;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
